<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Register - Facial Authentication</title>
    <link rel="stylesheet" href="styles/main.css">
    <script defer src="https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>User Registration</h1>
            <p class="subtitle">Register your face for secure authentication</p>
        </header>

        <main class="main-content">
            <div class="registration-container">
                <div class="step-indicator">
                    <div class="step active" id="step1">
                        <span class="step-number">1</span>
                        <span class="step-label">User Info</span>
                    </div>
                    <div class="step" id="step2">
                        <span class="step-number">2</span>
                        <span class="step-label">Face Capture</span>
                    </div>
                    <div class="step" id="step3">
                        <span class="step-number">3</span>
                        <span class="step-label">Confirmation</span>
                    </div>
                </div>

                <!-- Step 1: User Information -->
                <div class="registration-step" id="userInfoStep">
                    <h2>Enter Your Information</h2>
                    <form class="user-form" id="userForm">
                        <div class="form-group">
                            <label for="username">Username</label>
                            <input type="text" id="username" name="username" required 
                                   placeholder="Enter a unique username" minlength="3" maxlength="20">
                            <span class="form-help">3-20 characters, letters and numbers only</span>
                        </div>
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="email" id="email" name="email" required 
                                   placeholder="<EMAIL>">
                        </div>
                        <div class="form-group">
                            <label for="fullName">Full Name</label>
                            <input type="text" id="fullName" name="fullName" required 
                                   placeholder="Your full name">
                        </div>
                        <button type="button" class="btn btn-primary" onclick="proceedToFaceCapture()">
                            Next: Capture Face
                        </button>
                    </form>
                </div>

                <!-- Step 2: Face Capture -->
                <div class="registration-step hidden" id="faceCaptureStep">
                    <h2>Capture Your Face</h2>
                    <div class="camera-container">
                        <video id="video" autoplay muted playsinline></video>
                        <canvas id="overlay" class="overlay-canvas"></canvas>
                        <div class="camera-instructions">
                            <p>Position your face in the center of the frame</p>
                            <ul>
                                <li>Look directly at the camera</li>
                                <li>Ensure good lighting</li>
                                <li>Remove glasses if possible</li>
                                <li>Keep your face still</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="capture-controls">
                        <div class="detection-status" id="detectionStatus">
                            <span class="status-text">Initializing camera...</span>
                        </div>
                        <button class="btn btn-primary" id="captureBtn" onclick="captureFace()" disabled>
                            Capture Face
                        </button>
                        <button class="btn btn-secondary" onclick="retryCapture()">
                            Retry
                        </button>
                    </div>
                </div>

                <!-- Step 3: Confirmation -->
                <div class="registration-step hidden" id="confirmationStep">
                    <h2>Registration Complete!</h2>
                    <div class="success-message">
                        <div class="success-icon">✅</div>
                        <p>Your face has been successfully registered!</p>
                        <div class="user-summary" id="userSummary"></div>
                    </div>
                    <div class="action-buttons">
                        <button class="btn btn-primary" onclick="navigateToLogin()">
                            Login Now
                        </button>
                        <button class="btn btn-secondary" onclick="navigateToHome()">
                            Back to Home
                        </button>
                    </div>
                </div>
            </div>

            <div class="back-button">
                <button class="btn btn-text" onclick="navigateToHome()">
                    ← Back to Home
                </button>
            </div>
        </main>
    </div>

    <div class="loading-overlay hidden" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <p class="loading-text">Processing your face data...</p>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/camera.js"></script>
    <script src="js/faceAuth.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
