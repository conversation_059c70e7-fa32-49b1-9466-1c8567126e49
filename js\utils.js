/**
 * Utility functions for the facial authentication system
 */

// Navigation functions
function navigateToHome() {
    window.location.href = 'index.html';
}

function navigateToRegister() {
    window.location.href = 'register.html';
}

function navigateToLogin() {
    window.location.href = 'login.html';
}

function navigateToDashboard() {
    window.location.href = 'dashboard.html';
}

// DOM utility functions
function showElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.remove('hidden');
    }
}

function hideElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.add('hidden');
    }
}

function toggleElement(elementId) {
    const element = document.getElementById(elementId);
    if (element) {
        element.classList.toggle('hidden');
    }
}

function setElementText(elementId, text) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = text;
    }
}

function setElementHTML(elementId, html) {
    const element = document.getElementById(elementId);
    if (element) {
        element.innerHTML = html;
    }
}

// Status update functions
function updateStatus(elementId, status, text) {
    const element = document.getElementById(elementId);
    if (element) {
        element.textContent = text;
        element.className = 'status-value ' + status;
    }
}

function updateDetectionStatus(status, text) {
    const statusElement = document.getElementById('detectionStatus');
    const statusText = statusElement?.querySelector('.status-text');
    
    if (statusElement) {
        statusElement.className = 'detection-status ' + status;
    }
    
    if (statusText) {
        statusText.textContent = text;
    } else if (statusElement) {
        statusElement.textContent = text;
    }
}

// Loading overlay functions
function showLoading(text = 'Loading...') {
    const overlay = document.getElementById('loadingOverlay');
    const loadingText = overlay?.querySelector('.loading-text');
    
    if (overlay) {
        overlay.classList.remove('hidden');
    }
    
    if (loadingText && text) {
        loadingText.textContent = text;
    }
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.add('hidden');
    }
}

// Progress bar functions
function updateProgress(percentage) {
    const progressFill = document.getElementById('progressFill');
    if (progressFill) {
        progressFill.style.width = percentage + '%';
    }
}

// Form validation functions
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validateUsername(username) {
    const usernameRegex = /^[a-zA-Z0-9]{3,20}$/;
    return usernameRegex.test(username);
}

function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return false;
    
    const inputs = form.querySelectorAll('input[required]');
    let isValid = true;
    
    inputs.forEach(input => {
        if (!input.value.trim()) {
            isValid = false;
            input.style.borderColor = '#e53e3e';
        } else {
            input.style.borderColor = '#e2e8f0';
            
            // Additional validation based on input type
            if (input.type === 'email' && !validateEmail(input.value)) {
                isValid = false;
                input.style.borderColor = '#e53e3e';
            }
            
            if (input.name === 'username' && !validateUsername(input.value)) {
                isValid = false;
                input.style.borderColor = '#e53e3e';
            }
        }
    });
    
    return isValid;
}

// Error handling functions
function showError(message, elementId = null) {
    if (elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = message;
            element.className = 'error-message';
        }
    } else {
        console.error('Error:', message);
        alert('Error: ' + message);
    }
}

function showSuccess(message, elementId = null) {
    if (elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = message;
            element.className = 'success-message';
        }
    } else {
        console.log('Success:', message);
    }
}

// Date and time formatting
function formatDate(date) {
    return new Intl.DateTimeFormat('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}

function formatRelativeTime(date) {
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);
    
    if (diffInSeconds < 60) {
        return 'Just now';
    } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60);
        return `${minutes} minute${minutes > 1 ? 's' : ''} ago`;
    } else if (diffInSeconds < 86400) {
        const hours = Math.floor(diffInSeconds / 3600);
        return `${hours} hour${hours > 1 ? 's' : ''} ago`;
    } else {
        const days = Math.floor(diffInSeconds / 86400);
        return `${days} day${days > 1 ? 's' : ''} ago`;
    }
}

// Browser detection and capability checking
function getBrowserInfo() {
    const userAgent = navigator.userAgent;
    let browserName = 'Unknown';
    
    if (userAgent.includes('Chrome')) {
        browserName = 'Chrome';
    } else if (userAgent.includes('Firefox')) {
        browserName = 'Firefox';
    } else if (userAgent.includes('Safari')) {
        browserName = 'Safari';
    } else if (userAgent.includes('Edge')) {
        browserName = 'Edge';
    }
    
    return browserName;
}

function checkCameraSupport() {
    return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
}

function checkIndexedDBSupport() {
    return 'indexedDB' in window;
}

// Encryption utilities (simple base64 encoding for demo purposes)
function encodeData(data) {
    try {
        return btoa(JSON.stringify(data));
    } catch (error) {
        console.error('Error encoding data:', error);
        return null;
    }
}

function decodeData(encodedData) {
    try {
        return JSON.parse(atob(encodedData));
    } catch (error) {
        console.error('Error decoding data:', error);
        return null;
    }
}

// Generate unique ID
function generateId() {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
}

// Debounce function for performance optimization
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Copy to clipboard function
async function copyToClipboard(text) {
    try {
        await navigator.clipboard.writeText(text);
        return true;
    } catch (error) {
        console.error('Failed to copy to clipboard:', error);
        return false;
    }
}

// Download data as file
function downloadData(data, filename, type = 'application/json') {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
}

// Event listener helpers
function addEventListeners() {
    // Add global error handler
    window.addEventListener('error', (event) => {
        console.error('Global error:', event.error);
    });
    
    // Add unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
        console.error('Unhandled promise rejection:', event.reason);
    });
}

// Initialize utilities when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    addEventListeners();
});

// Export functions for use in other modules
window.Utils = {
    navigateToHome,
    navigateToRegister,
    navigateToLogin,
    navigateToDashboard,
    showElement,
    hideElement,
    toggleElement,
    setElementText,
    setElementHTML,
    updateStatus,
    updateDetectionStatus,
    showLoading,
    hideLoading,
    updateProgress,
    validateEmail,
    validateUsername,
    validateForm,
    showError,
    showSuccess,
    formatDate,
    formatRelativeTime,
    getBrowserInfo,
    checkCameraSupport,
    checkIndexedDBSupport,
    encodeData,
    decodeData,
    generateId,
    debounce,
    copyToClipboard,
    downloadData
};
