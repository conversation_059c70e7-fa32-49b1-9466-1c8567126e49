<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - Facial Authentication</title>
    <link rel="stylesheet" href="styles/main.css">
    <script defer src="https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Face Authentication Login</h1>
            <p class="subtitle">Look at the camera to authenticate</p>
        </header>

        <main class="main-content">
            <div class="login-container">
                <div class="camera-section">
                    <div class="camera-container">
                        <video id="video" autoplay muted playsinline></video>
                        <canvas id="overlay" class="overlay-canvas"></canvas>
                        <div class="authentication-overlay" id="authOverlay">
                            <div class="auth-status" id="authStatus">
                                <div class="status-icon" id="statusIcon">📷</div>
                                <div class="status-text" id="statusText">Initializing camera...</div>
                                <div class="progress-bar" id="progressBar">
                                    <div class="progress-fill" id="progressFill"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="login-controls">
                    <div class="instructions">
                        <h3>Authentication Instructions</h3>
                        <ul>
                            <li>Position your face in the center of the frame</li>
                            <li>Look directly at the camera</li>
                            <li>Stay still during authentication</li>
                            <li>Ensure good lighting conditions</li>
                        </ul>
                    </div>

                    <div class="auth-actions">
                        <button class="btn btn-primary" id="startAuthBtn" onclick="startAuthentication()">
                            <span class="btn-icon">🔍</span>
                            Start Authentication
                        </button>
                        <button class="btn btn-secondary" onclick="retryAuthentication()" id="retryBtn" style="display: none;">
                            Try Again
                        </button>
                    </div>

                    <div class="alternative-options">
                        <p>Having trouble?</p>
                        <button class="btn btn-text" onclick="showTroubleshooting()">
                            Troubleshooting Tips
                        </button>
                        <button class="btn btn-text" onclick="navigateToRegister()">
                            Register New Face
                        </button>
                    </div>
                </div>
            </div>

            <div class="back-button">
                <button class="btn btn-text" onclick="navigateToHome()">
                    ← Back to Home
                </button>
            </div>
        </main>
    </div>

    <!-- Authentication Result Modal -->
    <div class="modal hidden" id="resultModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">Authentication Result</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="result-icon" id="resultIcon"></div>
                <p id="resultMessage"></p>
                <div class="user-info" id="userInfo" style="display: none;"></div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-primary" id="modalActionBtn" onclick="handleModalAction()">
                    Continue
                </button>
            </div>
        </div>
    </div>

    <!-- Troubleshooting Modal -->
    <div class="modal hidden" id="troubleshootingModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Troubleshooting Tips</h3>
                <button class="modal-close" onclick="closeTroubleshooting()">&times;</button>
            </div>
            <div class="modal-body">
                <div class="troubleshooting-tips">
                    <h4>Camera Issues:</h4>
                    <ul>
                        <li>Allow camera permissions when prompted</li>
                        <li>Check if another application is using the camera</li>
                        <li>Try refreshing the page</li>
                    </ul>
                    
                    <h4>Recognition Issues:</h4>
                    <ul>
                        <li>Ensure adequate lighting</li>
                        <li>Remove glasses or hats if possible</li>
                        <li>Position your face clearly in the frame</li>
                        <li>Stay still during authentication</li>
                    </ul>
                    
                    <h4>Still having problems?</h4>
                    <p>You may need to register your face again with better conditions.</p>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeTroubleshooting()">
                    Close
                </button>
                <button class="btn btn-primary" onclick="navigateToRegister()">
                    Re-register Face
                </button>
            </div>
        </div>
    </div>

    <div class="loading-overlay hidden" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <p class="loading-text">Authenticating...</p>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/camera.js"></script>
    <script src="js/faceAuth.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
