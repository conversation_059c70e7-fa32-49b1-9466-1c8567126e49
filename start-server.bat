@echo off
title Facial Authentication System Server

echo.
echo ========================================
echo  Facial Authentication System Server
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    echo.
    pause
    exit /b 1
)

REM Check if required files exist
if not exist "index.html" (
    echo ERROR: index.html not found
    echo Please ensure you're running this from the correct directory
    echo.
    pause
    exit /b 1
)

echo Starting server...
echo.
echo Instructions:
echo 1. Your browser should open automatically
echo 2. If not, navigate to: http://localhost:8000
echo 3. Allow camera permissions when prompted
echo 4. Press Ctrl+C to stop the server
echo.

REM Start the Python server
python server.py

echo.
echo Server stopped. Press any key to exit...
pause >nul
