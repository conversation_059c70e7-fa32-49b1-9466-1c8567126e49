<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web-Based Facial Authentication System - Project Presentation</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            overflow: hidden;
        }

        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
        }

        .slide {
            width: 100%;
            height: 100%;
            display: none;
            padding: 40px;
            background: white;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            position: absolute;
            top: 0;
            left: 0;
            overflow-y: auto;
        }

        .slide.active {
            display: block;
            animation: slideIn 0.5s ease-in-out;
        }

        @keyframes slideIn {
            from { opacity: 0; transform: translateX(50px); }
            to { opacity: 1; transform: translateX(0); }
        }

        .slide-header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #667eea;
            padding-bottom: 20px;
        }

        .slide-title {
            font-size: 2.5em;
            color: #667eea;
            margin-bottom: 10px;
            font-weight: bold;
        }

        .slide-subtitle {
            font-size: 1.2em;
            color: #666;
            font-style: italic;
        }

        .slide-content {
            font-size: 1.1em;
            line-height: 1.6;
            text-align: justify;
        }

        .slide-content h2 {
            color: #764ba2;
            margin: 20px 0 15px 0;
            font-size: 1.5em;
        }

        .slide-content h3 {
            color: #667eea;
            margin: 15px 0 10px 0;
            font-size: 1.3em;
        }

        .slide-content ul {
            margin: 15px 0;
            padding-left: 30px;
        }

        .slide-content li {
            margin: 8px 0;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .architecture-diagram {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }

        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
        }

        .screenshot-container {
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            background: white;
        }

        .screenshot-container img {
            width: 100%;
            height: auto;
            display: block;
            border-radius: 10px;
        }

        .screenshot-caption {
            padding: 15px;
            background: #f8f9fa;
            text-align: center;
            font-size: 0.9em;
            color: #666;
            border-top: 1px solid #e9ecef;
        }

        .screenshot-placeholder {
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%),
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%),
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            border: 2px dashed #ccc;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            border-radius: 10px;
            color: #666;
            font-style: italic;
        }

        .navigation {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .nav-btn {
            padding: 12px 20px;
            background: #667eea;
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-btn:hover {
            background: #764ba2;
            transform: translateY(-2px);
            box-shadow: 0 7px 20px rgba(0,0,0,0.3);
        }

        .nav-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            z-index: 1000;
        }

        .progress-bar {
            position: fixed;
            top: 0;
            left: 0;
            height: 4px;
            background: #667eea;
            transition: width 0.3s ease;
            z-index: 1000;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 20px 0;
        }

        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }

        @media (max-width: 768px) {
            .slide {
                padding: 20px;
            }
            
            .slide-title {
                font-size: 2em;
            }
            
            .two-column {
                grid-template-columns: 1fr;
            }
            
            .feature-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <div class="progress-bar" id="progressBar"></div>
        <div class="slide-counter" id="slideCounter">1 / 13</div>

        <!-- Slide 1: Title -->
        <div class="slide active">
            <div class="slide-header">
                <h1 class="slide-title">Web-Based Facial Authentication System</h1>
                <p class="slide-subtitle">Secure Biometric Authentication for Modern Web Applications</p>
            </div>
            <div class="slide-content">
                <div class="highlight-box">
                    <h2>🎯 Project Overview</h2>
                    <p>A comprehensive facial recognition authentication system built with vanilla HTML, CSS, and JavaScript, providing secure biometric authentication using face-api.js for face detection and recognition.</p>
                </div>
                
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🔒</div>
                        <h3>Secure</h3>
                        <p>Local biometric data storage with encryption</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3>Fast</h3>
                        <p>Real-time authentication under 2 seconds</p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">📱</div>
                        <h3>Universal</h3>
                        <p>Cross-platform browser compatibility</p>
                    </div>
                </div>

                <div class="two-column">
                    <div>
                        <h3>Technology Stack:</h3>
                        <ul>
                            <li>Vanilla HTML5, CSS3, JavaScript ES6+</li>
                            <li>face-api.js for facial recognition</li>
                            <li>IndexedDB for secure local storage</li>
                            <li>WebRTC getUserMedia API</li>
                        </ul>
                    </div>
                    <div>
                        <h3>Key Features:</h3>
                        <ul>
                            <li>User registration with face capture</li>
                            <li>Real-time facial authentication</li>
                            <li>Secure biometric data encryption</li>
                            <li>Responsive design interface</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 2: Abstract -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">Abstract</h1>
                <p class="slide-subtitle">Project Summary and Objectives</p>
            </div>
            <div class="slide-content">
                <p>The Web-Based Facial Authentication System is an innovative biometric security solution that leverages advanced facial recognition technology to provide secure user authentication. Built entirely with vanilla web technologies, the system eliminates the need for traditional password-based authentication by utilizing facial biometric data for user identification and verification.</p>

                <h2>🎯 Key Objectives</h2>
                <ul>
                    <li>Implement secure facial recognition without external dependencies</li>
                    <li>Provide real-time biometric authentication capabilities</li>
                    <li>Ensure complete data privacy with local storage</li>
                    <li>Create responsive, user-friendly interface</li>
                    <li>Demonstrate practical ML integration in web browsers</li>
                </ul>

                <h2>🔬 Technical Innovation</h2>
                <p>The system incorporates real-time face detection using the face-api.js library, secure local data storage through IndexedDB, and a responsive user interface that works across desktop and mobile platforms. The implementation demonstrates the practical application of machine learning in web development, showcasing how modern browsers can handle complex biometric processing without requiring server-side infrastructure.</p>

                <div class="highlight-box">
                    <h3>Privacy-First Approach</h3>
                    <p>This approach ensures data privacy by keeping all biometric information locally stored on the user's device, eliminating concerns about external data breaches and unauthorized access.</p>
                </div>
            </div>
        </div>

        <!-- Slide 3: Domain Description -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">Domain Description</h1>
                <p class="slide-subtitle">Biometric Security and Web Authentication</p>
            </div>
            <div class="slide-content">
                <div class="two-column">
                    <div>
                        <h2>🔐 Biometric Authentication</h2>
                        <ul>
                            <li>Facial recognition technology for identity verification</li>
                            <li>Biometric data processing and storage</li>
                            <li>Security protocols for sensitive data handling</li>
                            <li>Privacy-preserving authentication methods</li>
                        </ul>

                        <h2>🌐 Web Technology Integration</h2>
                        <ul>
                            <li>Browser-based computer vision processing</li>
                            <li>Real-time camera access and video processing</li>
                            <li>Client-side machine learning model execution</li>
                            <li>Local data storage and encryption</li>
                        </ul>
                    </div>
                    <div>
                        <h2>🛡️ Security Considerations</h2>
                        <ul>
                            <li>Biometric data protection and encryption</li>
                            <li>Session management and user privacy</li>
                            <li>Secure authentication protocols</li>
                            <li>Data retention and deletion policies</li>
                        </ul>

                        <h2>👤 User Experience Design</h2>
                        <ul>
                            <li>Intuitive registration and authentication flows</li>
                            <li>Real-time feedback and error handling</li>
                            <li>Responsive design for multiple devices</li>
                            <li>Accessibility and usability considerations</li>
                        </ul>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3>🎯 Domain Impact</h3>
                    <p>The facial authentication domain encompasses computer vision, machine learning, web security, and user experience design, representing a convergence of multiple technological disciplines to create secure, user-friendly authentication solutions.</p>
                </div>
            </div>
        </div>

        <!-- Slide 4: Literature Survey -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">Literature Survey</h1>
                <p class="slide-subtitle">Research Background and Related Work</p>
            </div>
            <div class="slide-content">
                <h2>📚 Facial Recognition Technology</h2>
                <p>Research in facial recognition has evolved significantly with deep learning advancements. Modern systems use convolutional neural networks (CNNs) for feature extraction and comparison. The face-api.js library implements state-of-the-art models including TinyFaceDetector for efficient face detection and FaceRecognitionNet for generating facial descriptors.</p>

                <h2>🌐 Web-Based Biometric Systems</h2>
                <p>Studies show increasing adoption of biometric authentication in web applications due to improved security and user convenience. Browser capabilities have expanded to support real-time video processing and machine learning inference, enabling client-side biometric processing.</p>

                <div class="two-column">
                    <div>
                        <h3>🔒 Privacy and Security Research</h3>
                        <ul>
                            <li>Local data processing for biometric systems</li>
                            <li>Prevention of data breaches</li>
                            <li>Unauthorized access protection</li>
                            <li>GDPR compliance considerations</li>
                        </ul>
                    </div>
                    <div>
                        <h3>👥 Usability Studies</h3>
                        <ul>
                            <li>Better usability vs traditional passwords</li>
                            <li>Faster authentication times</li>
                            <li>Reduced cognitive load for users</li>
                            <li>Improved user experience metrics</li>
                        </ul>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3>🔬 Research Gap Addressed</h3>
                    <p>Our system addresses the gap between advanced facial recognition capabilities and practical web implementation, providing a complete client-side solution that maintains privacy while delivering enterprise-grade security.</p>
                </div>
            </div>
        </div>

        <!-- Slide 5: Existing System and Drawbacks -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">Existing System and Drawbacks</h1>
                <p class="slide-subtitle">Current Solutions and Their Limitations</p>
            </div>
            <div class="slide-content">
                <div class="two-column">
                    <div>
                        <h2>🔑 Traditional Password Systems</h2>
                        <ul>
                            <li>Security vulnerabilities (weak passwords, reuse, phishing)</li>
                            <li>Poor user experience (forgotten passwords, complex requirements)</li>
                            <li>Maintenance overhead (password resets, account recovery)</li>
                            <li>Susceptible to brute force and dictionary attacks</li>
                        </ul>

                        <h2>🏢 Existing Biometric Solutions</h2>
                        <ul>
                            <li>Server-dependent systems requiring cloud processing</li>
                            <li>Privacy concerns with centralized biometric data storage</li>
                            <li>High infrastructure costs and complexity</li>
                            <li>Limited browser compatibility and mobile support</li>
                        </ul>
                    </div>
                    <div>
                        <h2>🌐 Current Web Authentication</h2>
                        <ul>
                            <li>Two-factor authentication adds complexity</li>
                            <li>SMS-based verification has security vulnerabilities</li>
                            <li>Hardware tokens require additional devices</li>
                            <li>OAuth systems depend on third-party services</li>
                        </ul>

                        <h2>⚠️ Major Drawbacks</h2>
                        <ul>
                            <li>Data privacy concerns with cloud-based processing</li>
                            <li>Dependency on external services and internet connectivity</li>
                            <li>Complex implementation requiring specialized infrastructure</li>
                            <li>Limited customization and control over authentication flow</li>
                        </ul>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3>💡 Problem Statement</h3>
                    <p>Current authentication systems either compromise security (passwords) or privacy (cloud-based biometrics), while adding complexity and dependencies that hinder widespread adoption.</p>
                </div>
            </div>
        </div>

        <!-- Slide 6: Proposed System and Advantages -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">Proposed System and Advantages</h1>
                <p class="slide-subtitle">Our Solution and Benefits</p>
            </div>
            <div class="slide-content">
                <h2>💡 System Overview</h2>
                <p>The proposed Web-Based Facial Authentication System addresses existing limitations by implementing a complete client-side biometric authentication solution using modern web technologies.</p>

                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>🔒 Security Benefits</h3>
                        <ul>
                            <li>Enhanced data privacy with local storage</li>
                            <li>Elimination of password-related vulnerabilities</li>
                            <li>Encrypted biometric data storage</li>
                            <li>No external data transmission</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>👤 User Experience Improvements</h3>
                        <ul>
                            <li>Faster authentication (under 2 seconds)</li>
                            <li>Elimination of password management</li>
                            <li>Intuitive and user-friendly interface</li>
                            <li>Seamless cross-device compatibility</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>⚙️ Technical Advantages</h3>
                        <ul>
                            <li>No server infrastructure required</li>
                            <li>Reduced operational costs</li>
                            <li>Easy deployment and maintenance</li>
                            <li>Scalable client-side architecture</li>
                        </ul>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3>🛡️ Privacy Protection</h3>
                    <p>Complete user control over biometric data • Local data processing and storage • Data export and deletion capabilities • GDPR compliance features</p>
                </div>
            </div>
        </div>

        <!-- Slide 7: Requirements -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">System Requirements</h1>
                <p class="slide-subtitle">Software and Hardware Specifications</p>
            </div>
            <div class="slide-content">
                <div class="two-column">
                    <div>
                        <h2>💻 Software Requirements</h2>
                        <h3>Browser Support:</h3>
                        <ul>
                            <li>Chrome 60+ (Recommended)</li>
                            <li>Firefox 55+</li>
                            <li>Safari 11+</li>
                            <li>Microsoft Edge 79+</li>
                        </ul>

                        <h3>Required Browser Features:</h3>
                        <ul>
                            <li>WebRTC getUserMedia API</li>
                            <li>IndexedDB for local storage</li>
                            <li>ES6+ JavaScript support</li>
                            <li>Canvas API for video processing</li>
                        </ul>

                        <h3>Development Tools:</h3>
                        <ul>
                            <li>Text editor or IDE</li>
                            <li>Python 3.x for development server</li>
                            <li>Modern web browser for testing</li>
                            <li>Git for version control</li>
                        </ul>
                    </div>
                    <div>
                        <h2>🖥️ Hardware Requirements</h2>
                        <h3>Minimum Specifications:</h3>
                        <ul>
                            <li>Camera (webcam or built-in)</li>
                            <li>2GB RAM minimum</li>
                            <li>Modern CPU for face processing</li>
                            <li>Internet connection for model loading</li>
                        </ul>

                        <h3>Recommended Specifications:</h3>
                        <ul>
                            <li>HD webcam for better accuracy</li>
                            <li>4GB+ RAM for optimal performance</li>
                            <li>Multi-core processor</li>
                            <li>Stable internet connection</li>
                        </ul>

                        <h3>Deployment Requirements:</h3>
                        <ul>
                            <li>Web server (Apache, Nginx, Python)</li>
                            <li>HTTPS certificate for production</li>
                            <li>Domain name for hosting</li>
                        </ul>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3>🔧 Dependencies</h3>
                    <p>face-api.js v0.22.2 for facial recognition • No additional frameworks required • Vanilla web technologies only</p>
                </div>
            </div>
        </div>

        <!-- Slide 8: Modules Description -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">Modules Description</h1>
                <p class="slide-subtitle">System Components and Architecture</p>
            </div>
            <div class="slide-content">
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>📱 APP.JS Module</h3>
                        <p><strong>Main Application Logic</strong></p>
                        <ul>
                            <li>Application initialization and coordination</li>
                            <li>Page navigation and routing</li>
                            <li>Event handling and user interactions</li>
                            <li>Integration between different modules</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>🧠 FACEAUTH.JS Module</h3>
                        <p><strong>Facial Recognition Engine</strong></p>
                        <ul>
                            <li>face-api.js model loading and initialization</li>
                            <li>Face detection and landmark identification</li>
                            <li>Facial descriptor extraction and comparison</li>
                            <li>Authentication logic and confidence scoring</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>📷 CAMERA.JS Module</h3>
                        <p><strong>Camera Management</strong></p>
                        <ul>
                            <li>WebRTC camera access and stream management</li>
                            <li>Video element control and configuration</li>
                            <li>Camera device enumeration and switching</li>
                            <li>Photo capture and frame processing</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>💾 STORAGE.JS Module</h3>
                        <p><strong>Data Management</strong></p>
                        <ul>
                            <li>IndexedDB database initialization</li>
                            <li>User data storage and retrieval</li>
                            <li>Session management and tracking</li>
                            <li>Data encryption and security</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>🔧 UTILS.JS Module</h3>
                        <p><strong>Utility Functions</strong></p>
                        <ul>
                            <li>Common utility functions and helpers</li>
                            <li>Data encoding and decoding</li>
                            <li>Status updates and UI feedback</li>
                            <li>ID generation and validation</li>
                        </ul>
                    </div>
                    <div class="feature-card">
                        <h3>🎨 HTML/CSS Pages</h3>
                        <p><strong>User Interface</strong></p>
                        <ul>
                            <li>index.html: Landing page and overview</li>
                            <li>register.html: User registration interface</li>
                            <li>login.html: Authentication interface</li>
                            <li>dashboard.html: User management</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 9: Architecture -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">System Architecture</h1>
                <p class="slide-subtitle">Client-Side Architecture Design</p>
            </div>
            <div class="slide-content">
                <div class="architecture-diagram">
                    <h3>CLIENT-SIDE ARCHITECTURE:</h3>
                    <pre>
┌─────────────────────────────────────────────────────────────────┐
│                        PRESENTATION LAYER                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │  index.html │ │register.html│ │ login.html  │ │dashboard.html││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│                       APPLICATION LAYER                         │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │   app.js    │ │ faceAuth.js │ │  camera.js  │ │  utils.js   ││
│  │(Controller) │ │(Recognition)│ │(Hardware)   │ │(Utilities)  ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
┌─────────────────────────────────────────────────────────────────┐
│                         DATA LAYER                              │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐│
│  │ storage.js  │ │  IndexedDB  │ │face-api.js  │ │   WebRTC    ││
│  │(Data Access)│ │(Local Store)│ │(ML Models)  │ │(Camera API) ││
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘│
└─────────────────────────────────────────────────────────────────┘
                    </pre>
                </div>

                <div class="two-column">
                    <div>
                        <h3>🔄 Data Flow Architecture</h3>
                        <ul>
                            <li><strong>Registration Flow:</strong> Camera → Face Detection → Descriptor Extraction → Storage</li>
                            <li><strong>Authentication Flow:</strong> Camera → Face Detection → Comparison → Verification</li>
                            <li><strong>Session Management:</strong> Storage → Session Tracking → User State Management</li>
                        </ul>
                    </div>
                    <div>
                        <h3>🛡️ Security Architecture</h3>
                        <ul>
                            <li>Client-side encryption for biometric data</li>
                            <li>Local storage isolation</li>
                            <li>No external data transmission</li>
                            <li>Session-based access control</li>
                        </ul>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3>🏗️ Component Interactions</h3>
                    <p>1. User Interface Layer handles user interactions and displays • 2. Application Layer processes business logic and coordinates modules • 3. Data Layer manages storage, ML models, and hardware access</p>
                </div>
            </div>
        </div>

        <!-- Slide 10: UML Diagrams -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">UML Diagrams</h1>
                <p class="slide-subtitle">System Design and Interactions</p>
            </div>
            <div class="slide-content">
                <div class="two-column">
                    <div>
                        <h3>📊 Class Diagram - Core Classes</h3>
                        <div class="architecture-diagram">
                            <pre>
┌─────────────────────────────────┐
│           FaceAuth              │
├─────────────────────────────────┤
│ - isLoaded: boolean             │
│ - detectionOptions: Object      │
├─────────────────────────────────┤
│ + init(): Promise&lt;boolean&gt;      │
│ + detectFace(video): Promise    │
│ + extractFaceDescriptor(): Obj  │
│ + authenticateUser(): Promise   │
│ + compareFaces(): Object        │
└─────────────────────────────────┘

┌─────────────────────────────────┐
│        CameraManager           │
├─────────────────────────────────┤
│ - video: HTMLVideoElement       │
│ - stream: MediaStream           │
│ - isInitialized: boolean        │
├─────────────────────────────────┤
│ + init(): Promise&lt;boolean&gt;      │
│ + startCamera(): Promise&lt;void&gt;  │
│ + stopCamera(): void            │
│ + takePhoto(): string           │
└─────────────────────────────────┘
                            </pre>
                        </div>
                    </div>
                    <div>
                        <h3>🔄 Sequence Diagram - Authentication</h3>
                        <div class="architecture-diagram">
                            <pre>
User → UI → Camera → FaceAuth → Storage
 │     │      │        │         │
 │ Login      │        │         │
 │ ─────────→ │        │         │
 │     │ Start Camera  │         │
 │     │ ─────────────→│         │
 │     │      │ Detect │         │
 │     │      │ ─────→ │         │
 │     │      │        │ Get Users
 │     │      │        │ ───────→
 │     │      │        │ Compare │
 │     │      │        │ Faces   │
 │     │ Auth Result   │         │
 │ ←─────────────────────────────│
                            </pre>
                        </div>

                        <h3>📝 Storage Class</h3>
                        <div class="architecture-diagram">
                            <pre>
┌─────────────────────────────────┐
│      FaceAuthStorage            │
├─────────────────────────────────┤
│ - db: IDBDatabase               │
│ - dbName: string                │
├─────────────────────────────────┤
│ + saveUser(): Promise&lt;Object&gt;   │
│ + getUser(): Promise&lt;Object&gt;    │
│ + deleteUser(): Promise&lt;bool&gt;   │
└─────────────────────────────────┘
                            </pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 11: Implementation -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">Implementation</h1>
                <p class="slide-subtitle">Algorithms, Coding & Testing</p>
            </div>
            <div class="slide-content">
                <h2>🧠 Core Algorithms</h2>
                <div class="two-column">
                    <div>
                        <h3>Face Detection Algorithm:</h3>
                        <ol>
                            <li>Initialize TinyFaceDetector with optimized parameters</li>
                            <li>Process video frame through neural network</li>
                            <li>Apply confidence threshold filtering (0.3)</li>
                            <li>Return bounding box coordinates and landmarks</li>
                        </ol>

                        <h3>Face Recognition Algorithm:</h3>
                        <ol>
                            <li>Extract 68 facial landmarks from detected face</li>
                            <li>Generate 128-dimensional face descriptor vector</li>
                            <li>Normalize descriptor for consistent comparison</li>
                            <li>Store encrypted descriptor in IndexedDB</li>
                        </ol>
                    </div>
                    <div>
                        <h3>Face Comparison Algorithm:</h3>
                        <ol>
                            <li>Calculate Euclidean distance between descriptors</li>
                            <li>Apply similarity threshold (0.6 for matching)</li>
                            <li>Return confidence score and match result</li>
                            <li>Select best match from multiple candidates</li>
                        </ol>

                        <h3>Testing Methodology:</h3>
                        <ul>
                            <li><strong>Unit Testing:</strong> Individual module functionality</li>
                            <li><strong>Integration Testing:</strong> End-to-end flow validation</li>
                            <li><strong>Performance Testing:</strong> Speed and memory optimization</li>
                            <li><strong>Security Testing:</strong> Data encryption validation</li>
                        </ul>
                    </div>
                </div>

                <h3>💻 Key Implementation Code:</h3>
                <div class="code-block">
async extractFaceDescriptor(videoElement) {
    const detection = await faceapi
        .detectSingleFace(videoElement, this.detectionOptions)
        .withFaceLandmarks()
        .withFaceDescriptor();

    if (!detection) {
        throw new Error('No face detected');
    }

    return {
        descriptor: Array.from(detection.descriptor),
        landmarks: detection.landmarks,
        detection: detection.detection
    };
}
                </div>
            </div>
        </div>

        <!-- Slide 12: Screenshots -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">System Screenshots</h1>
                <p class="slide-subtitle">User Interface and Features</p>
            </div>
            <div class="slide-content">
                <div class="two-column">
                    <div>
                        <h3>📱 Main Landing Page</h3>
                        <div class="screenshot-container">
                            <img src="screenshots/Screenshots/landing-page.png.png"
                                 alt="Main Landing Page"
                                 onerror="this.parentElement.innerHTML='<div class=screenshot-placeholder><p>📸 Screenshot not found: screenshots/Screenshots/landing-page.png.png</p><p>Professional gradient background design with feature cards highlighting security, speed, and universality</p></div>'">
                            <div class="screenshot-caption">
                                Professional gradient background design with feature cards highlighting security, speed, and universality
                            </div>
                        </div>

                        <h3>👤 User Registration Page</h3>
                        <div class="screenshot-container">
                            <img src="screenshots/Screenshots/registration.png.png"
                                 alt="User Registration Page"
                                 onerror="this.parentElement.innerHTML='<div class=screenshot-placeholder><p>📸 Screenshot not found: screenshots/Screenshots/registration.png.png</p><p>Step-by-step registration process with real-time camera preview and face detection overlay</p></div>'">
                            <div class="screenshot-caption">
                                Step-by-step registration process with real-time camera preview and face detection overlay
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3>🔐 Authentication Page</h3>
                        <div class="screenshot-container">
                            <img src="screenshots/Screenshots/login.png.png"
                                 alt="Authentication Page"
                                 onerror="this.parentElement.innerHTML='<div class=screenshot-placeholder><p>📸 Screenshot not found: screenshots/Screenshots/login.png.png</p><p>Clean login interface with camera preview and real-time face detection during authentication</p></div>'">
                            <div class="screenshot-caption">
                                Clean login interface with camera preview and real-time face detection during authentication
                            </div>
                        </div>

                        <h3>📊 User Dashboard</h3>
                        <div class="screenshot-container">
                            <img src="screenshots/Screenshots/dashboard.png.png"
                                 alt="User Dashboard"
                                 onerror="this.parentElement.innerHTML='<div class=screenshot-placeholder><p>📸 Screenshot not found: screenshots/Screenshots/dashboard.png.png</p><p>Comprehensive user profile information with authentication history and data management options</p></div>'">
                            <div class="screenshot-caption">
                                Comprehensive user profile information with authentication history and data management options
                            </div>
                        </div>
                    </div>
                </div>

                <h3>🎯 Face Detection Features</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h4>Real-time Detection</h4>
                        <div class="screenshot-container">
                            <img src="screenshots/Screenshots/face-detection.png.png"
                                 alt="Face Detection Overlay"
                                 onerror="this.parentElement.innerHTML='<div class=screenshot-placeholder><p>📸 Screenshot not found: screenshots/Screenshots/face-detection.png.png</p><p>Face detection overlay with bounding boxes</p></div>'">
                            <div class="screenshot-caption">
                                Real-time face detection with bounding boxes and landmarks
                            </div>
                        </div>
                    </div>
                    <div class="feature-card">
                        <h4>Mobile Responsive</h4>
                        <div class="screenshot-container">
                            <img src="screenshots/Screenshots/Screenshot 2025-06-03 211814.png"
                                 alt="Mobile Interface"
                                 onerror="this.parentElement.innerHTML='<div class=screenshot-placeholder><p>📸 Screenshot not found: screenshots/Screenshots/mobile-view.png</p><p>Mobile interface optimization</p></div>'">
                            <div class="screenshot-caption">
                                Optimized mobile interface with touch-friendly controls
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Slide 13: Conclusion -->
        <div class="slide">
            <div class="slide-header">
                <h1 class="slide-title">Conclusion & Future Work</h1>
                <p class="slide-subtitle">Project Summary and Next Steps</p>
            </div>
            <div class="slide-content">
                <div class="highlight-box">
                    <h2>🎯 Key Achievements</h2>
                    <p>The Web-Based Facial Authentication System successfully demonstrates the implementation of advanced biometric security using modern web technologies, achieving secure, user-friendly facial authentication without requiring external infrastructure or compromising user privacy.</p>
                </div>

                <div class="two-column">
                    <div>
                        <h3>✅ Technical Contributions</h3>
                        <ul>
                            <li>Complete client-side facial recognition implementation</li>
                            <li>Secure local storage of biometric data using IndexedDB</li>
                            <li>Real-time face detection and authentication capabilities</li>
                            <li>Responsive design supporting multiple devices and browsers</li>
                            <li>Privacy-preserving biometric authentication approach</li>
                        </ul>

                        <h3>🔬 Research Impact</h3>
                        <ul>
                            <li>Integration of machine learning models in web browsers</li>
                            <li>Efficient face recognition algorithms optimized for web performance</li>
                            <li>Modular architecture enabling easy maintenance and extension</li>
                        </ul>
                    </div>
                    <div>
                        <h3>🚀 Future Enhancements</h3>
                        <ul>
                            <li>Multi-factor authentication combining face and voice recognition</li>
                            <li>Advanced anti-spoofing measures using liveness detection</li>
                            <li>Cloud synchronization with end-to-end encryption</li>
                            <li>Integration with existing identity management systems</li>
                            <li>Support for additional biometric modalities</li>
                        </ul>

                        <h3>📚 References</h3>
                        <ul>
                            <li>face-api.js Documentation</li>
                            <li>WebRTC API Specifications</li>
                            <li>IndexedDB API Documentation</li>
                            <li>GDPR Compliance Guidelines</li>
                            <li>Biometric Security Standards</li>
                        </ul>
                    </div>
                </div>

                <div class="highlight-box">
                    <h3>🎉 Project Success</h3>
                    <p>This project successfully demonstrates a complete facial authentication system built entirely with vanilla web technologies, showcasing advanced JavaScript programming, modern web APIs, biometric technology integration, and professional software development practices.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Navigation Controls -->
    <div class="navigation">
        <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">← Previous</button>
        <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">Next →</button>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.slide');
        const totalSlides = slides.length;

        function showSlide(n) {
            slides[currentSlide].classList.remove('active');
            currentSlide = (n + totalSlides) % totalSlides;
            slides[currentSlide].classList.add('active');

            // Update progress bar
            const progress = ((currentSlide + 1) / totalSlides) * 100;
            document.getElementById('progressBar').style.width = progress + '%';

            // Update slide counter
            document.getElementById('slideCounter').textContent = `${currentSlide + 1} / ${totalSlides}`;

            // Update navigation buttons
            document.getElementById('prevBtn').disabled = currentSlide === 0;
            document.getElementById('nextBtn').disabled = currentSlide === totalSlides - 1;
        }

        function changeSlide(direction) {
            showSlide(currentSlide + direction);
        }

        // Keyboard navigation
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowLeft') {
                changeSlide(-1);
            } else if (event.key === 'ArrowRight') {
                changeSlide(1);
            } else if (event.key === 'Home') {
                showSlide(0);
            } else if (event.key === 'End') {
                showSlide(totalSlides - 1);
            }
        });

        // Initialize
        showSlide(0);

        // Auto-advance slides (optional - uncomment to enable)
        // setInterval(() => {
        //     if (currentSlide < totalSlides - 1) {
        //         changeSlide(1);
        //     }
        // }, 10000); // 10 seconds per slide
    </script>
</body>
</html>
