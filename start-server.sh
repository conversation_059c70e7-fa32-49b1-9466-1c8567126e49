#!/bin/bash

# Facial Authentication System Server Startup Script

echo ""
echo "========================================"
echo " Facial Authentication System Server"
echo "========================================"
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "❌ ERROR: Python is not installed"
        echo "Please install Python from https://python.org"
        echo ""
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# Check if we're in the right directory
if [ ! -f "index.html" ]; then
    echo "❌ ERROR: index.html not found"
    echo "Please ensure you're running this from the correct directory"
    echo ""
    exit 1
fi

echo "✅ Python found: $($PYTHON_CMD --version)"
echo "✅ Application files found"
echo ""

echo "🚀 Starting server..."
echo ""
echo "📋 Instructions:"
echo "1. Your browser should open automatically"
echo "2. If not, navigate to: http://localhost:8000"
echo "3. Allow camera permissions when prompted"
echo "4. Press Ctrl+C to stop the server"
echo ""

# Make the script executable if it isn't already
chmod +x "$0"

# Start the Python server
$PYTHON_CMD server.py

echo ""
echo "🛑 Server stopped."
