/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header Styles */
.header {
    text-align: center;
    margin-bottom: 2rem;
    color: white;
}

.header h1 {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.user-greeting {
    font-weight: 500;
}

/* Main Content */
.main-content {
    flex: 1;
    background: white;
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    margin-bottom: 2rem;
}

/* Welcome Section */
.welcome-section {
    text-align: center;
    margin-bottom: 3rem;
}

.welcome-section h2 {
    color: #4a5568;
    margin-bottom: 1rem;
}

.welcome-section p {
    color: #718096;
    max-width: 600px;
    margin: 0 auto;
}

/* Features Grid */
.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.feature-card {
    text-align: center;
    padding: 2rem;
    border-radius: 10px;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.feature-card h3 {
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.feature-card p {
    color: #718096;
    font-size: 0.9rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

.btn-danger {
    background: #e53e3e;
    color: white;
}

.btn-danger:hover {
    background: #c53030;
}

.btn-text {
    background: transparent;
    color: #667eea;
    padding: 8px 16px;
}

.btn-text:hover {
    background: rgba(102, 126, 234, 0.1);
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.btn-icon {
    font-size: 1.2rem;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
    margin-bottom: 3rem;
}

/* System Status */
.system-status {
    background: #f7fafc;
    border-radius: 10px;
    padding: 1.5rem;
    border: 1px solid #e2e8f0;
}

.status-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.status-item:last-child {
    border-bottom: none;
}

.status-label {
    font-weight: 500;
    color: #4a5568;
}

.status-value {
    font-weight: 600;
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.9rem;
}

.status-value.ready {
    background: #c6f6d5;
    color: #22543d;
}

.status-value.error {
    background: #fed7d7;
    color: #742a2a;
}

.status-value.loading {
    background: #bee3f8;
    color: #2a4365;
}

/* Registration Styles */
.registration-container {
    max-width: 800px;
    margin: 0 auto;
}

.step-indicator {
    display: flex;
    justify-content: center;
    margin-bottom: 3rem;
    gap: 2rem;
}

.step {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    opacity: 0.5;
    transition: opacity 0.3s ease;
}

.step.active {
    opacity: 1;
}

.step-number {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e2e8f0;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    color: #4a5568;
}

.step.active .step-number {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.step-label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #4a5568;
}

.registration-step {
    text-align: center;
}

.registration-step.hidden {
    display: none;
}

/* Form Styles */
.user-form {
    max-width: 400px;
    margin: 0 auto;
    text-align: left;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: #4a5568;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #e2e8f0;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
}

.form-help {
    font-size: 0.8rem;
    color: #718096;
    margin-top: 0.25rem;
    display: block;
}

/* Camera Styles */
.camera-container {
    position: relative;
    max-width: 640px;
    margin: 0 auto 2rem;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}

#video {
    width: 100%;
    height: auto;
    display: block;
}

.overlay-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.camera-instructions {
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 1rem;
    text-align: left;
}

.camera-instructions ul {
    margin-top: 0.5rem;
    padding-left: 1.5rem;
}

.camera-instructions li {
    margin-bottom: 0.25rem;
}

/* Authentication Overlay */
.authentication-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0,0,0,0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.auth-status {
    text-align: center;
    padding: 2rem;
}

.status-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.status-text {
    font-size: 1.2rem;
    margin-bottom: 1rem;
}

.progress-bar {
    width: 200px;
    height: 4px;
    background: rgba(255,255,255,0.3);
    border-radius: 2px;
    overflow: hidden;
    margin: 0 auto;
}

.progress-fill {
    height: 100%;
    background: #667eea;
    width: 0%;
    transition: width 0.3s ease;
}

/* Footer */
.footer {
    text-align: center;
    color: white;
    opacity: 0.8;
    font-size: 0.9rem;
}

/* Utility Classes */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

.mb-2 {
    margin-bottom: 1rem;
}

.mb-3 {
    margin-bottom: 1.5rem;
}

/* Dashboard Styles */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.dashboard-card {
    background: #f7fafc;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
    overflow: hidden;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.dashboard-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem;
}

.card-header h3 {
    margin: 0;
    font-size: 1.1rem;
}

.card-content {
    padding: 1.5rem;
}

.info-item, .security-item, .history-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #e2e8f0;
}

.info-item:last-child, .security-item:last-child, .history-item:last-child {
    border-bottom: none;
}

.info-label, .security-label {
    font-weight: 500;
    color: #4a5568;
}

.info-value {
    color: #2d3748;
    font-weight: 600;
}

.security-status {
    padding: 4px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.security-status.enabled {
    background: #c6f6d5;
    color: #22543d;
}

.history-time {
    font-size: 0.9rem;
    color: #718096;
}

.history-status {
    font-size: 0.9rem;
    font-weight: 500;
}

.history-status.success {
    color: #22543d;
}

.security-actions {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.quick-actions {
    text-align: center;
    margin-bottom: 2rem;
}

.quick-actions h3 {
    margin-bottom: 1.5rem;
    color: #4a5568;
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal.hidden {
    display: none;
}

.modal-content {
    background: white;
    border-radius: 15px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
    margin: 0;
    color: #2d3748;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #718096;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #4a5568;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1.5rem;
    border-top: 1px solid #e2e8f0;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

.result-icon {
    font-size: 4rem;
    text-align: center;
    margin-bottom: 1rem;
}

.success-message {
    text-align: center;
    margin-bottom: 2rem;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
}

.user-summary {
    background: #f7fafc;
    padding: 1rem;
    border-radius: 8px;
    margin-top: 1rem;
    text-align: left;
}

.troubleshooting-tips h4 {
    color: #4a5568;
    margin-bottom: 0.5rem;
    margin-top: 1rem;
}

.troubleshooting-tips h4:first-child {
    margin-top: 0;
}

.troubleshooting-tips ul {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
}

.troubleshooting-tips li {
    margin-bottom: 0.25rem;
    color: #718096;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0,0,0,0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    color: white;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1.1rem;
    text-align: center;
}

/* Capture Controls */
.capture-controls {
    text-align: center;
    margin-top: 2rem;
}

.detection-status {
    margin-bottom: 1rem;
    padding: 1rem;
    border-radius: 8px;
    background: #f7fafc;
    border: 1px solid #e2e8f0;
}

.detection-status.detecting {
    background: #bee3f8;
    border-color: #90cdf4;
    color: #2a4365;
}

.detection-status.ready {
    background: #c6f6d5;
    border-color: #9ae6b4;
    color: #22543d;
}

.detection-status.error {
    background: #fed7d7;
    border-color: #fbb6ce;
    color: #742a2a;
}

.login-controls {
    max-width: 500px;
    margin: 0 auto;
}

.instructions {
    background: #f7fafc;
    padding: 1.5rem;
    border-radius: 10px;
    margin-bottom: 2rem;
    border: 1px solid #e2e8f0;
}

.instructions h3 {
    color: #4a5568;
    margin-bottom: 1rem;
}

.instructions ul {
    padding-left: 1.5rem;
}

.instructions li {
    margin-bottom: 0.5rem;
    color: #718096;
}

.auth-actions {
    text-align: center;
    margin-bottom: 2rem;
}

.alternative-options {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid #e2e8f0;
}

.alternative-options p {
    color: #718096;
    margin-bottom: 1rem;
}

.back-button {
    margin-top: 2rem;
    text-align: center;
}

/* Test Page Styles */
.test-container {
    max-width: 1000px;
    margin: 0 auto;
}

.status-section {
    margin-bottom: 2rem;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-top: 1rem;
}

.camera-test-section {
    margin-bottom: 2rem;
    text-align: center;
}

.controls {
    margin-top: 1rem;
    display: flex;
    gap: 1rem;
    justify-content: center;
    flex-wrap: wrap;
}

.detection-info {
    margin-bottom: 2rem;
    background: #f7fafc;
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.debug-logs {
    margin-bottom: 2rem;
}

.troubleshooting {
    margin-bottom: 2rem;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.tip-card {
    background: #f7fafc;
    padding: 1.5rem;
    border-radius: 10px;
    border: 1px solid #e2e8f0;
}

.tip-card h4 {
    color: #4a5568;
    margin-bottom: 1rem;
}

.tip-card ul {
    padding-left: 1.5rem;
    margin: 0;
}

.tip-card li {
    margin-bottom: 0.5rem;
    color: #718096;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header h1 {
        font-size: 2rem;
    }

    .main-content {
        padding: 1.5rem;
    }

    .features-grid, .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 300px;
    }

    .step-indicator {
        gap: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
    }

    .security-actions {
        flex-direction: column;
    }

    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-footer {
        flex-direction: column;
    }
}
