# Web-Based Facial Authentication System

A comprehensive facial recognition authentication system built with vanilla HTML, CSS, and JavaScript. This system provides secure biometric authentication using face-api.js for face detection and recognition.

## Features

### Core Functionality
- **User Registration**: Capture and store facial biometric data securely
- **Facial Authentication**: Login using facial recognition technology
- **Real-time Face Detection**: Live camera feed with face detection overlay
- **Secure Data Storage**: Local storage using IndexedDB with data encryption
- **Responsive Design**: Works on desktop and mobile browsers

### Security Features
- Biometric data encryption using base64 encoding
- Local data storage (no server required)
- Session management
- Authentication history tracking
- Data export and deletion capabilities

### User Interface
- Professional, modern design with gradient backgrounds
- Step-by-step registration process
- Real-time feedback during authentication
- Comprehensive dashboard with user statistics
- Error handling and troubleshooting guides

## Technical Stack

- **Frontend**: Vanilla HTML5, CSS3, ES6+ JavaScript
- **Face Recognition**: face-api.js library
- **Storage**: IndexedDB for local data persistence
- **Camera**: WebRTC getUserMedia API
- **Styling**: CSS Grid, Flexbox, CSS animations

## File Structure

```
├── index.html              # Main landing page
├── register.html           # User registration page
├── login.html             # Authentication page
├── dashboard.html         # User dashboard
├── styles/
│   └── main.css          # Main stylesheet
├── js/
│   ├── app.js            # Main application logic
│   ├── faceAuth.js       # Facial recognition module
│   ├── camera.js         # Camera management
│   ├── storage.js        # IndexedDB operations
│   └── utils.js          # Utility functions
└── README.md             # This file
```

## Setup Instructions

### Prerequisites
- Modern web browser with camera support
- HTTPS connection (required for camera access)
- IndexedDB support

### Installation

1. **Clone or download the project files**
   ```bash
   git clone <repository-url>
   cd web-based-facial-authentication
   ```

2. **Serve the files over HTTPS**
   
   The application requires HTTPS to access the camera. You can use any of these methods:

   **Option A: Using Python (if installed)**
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   ```

   **Option B: Using Node.js http-server**
   ```bash
   npm install -g http-server
   http-server -p 8000
   ```

   **Option C: Using Live Server (VS Code extension)**
   - Install the Live Server extension in VS Code
   - Right-click on index.html and select "Open with Live Server"

3. **Access the application**
   - Open your browser and navigate to `http://localhost:8000`
   - Allow camera permissions when prompted

### Face-API.js Models

The application uses face-api.js models for face detection and recognition. The models are loaded from CDN by default, but for better performance, you can download them locally:

1. Create a `models` directory in the project root
2. Download the required models from the [face-api.js repository](https://github.com/justadudewhohacks/face-api.js/tree/master/weights)
3. Update the model loading paths in `js/faceAuth.js`

Required models:
- `tiny_face_detector_model-weights_manifest.json`
- `tiny_face_detector_model-shard1`
- `face_landmark_68_model-weights_manifest.json`
- `face_landmark_68_model-shard1`
- `face_recognition_model-weights_manifest.json`
- `face_recognition_model-shard1`
- `face_expression_model-weights_manifest.json`
- `face_expression_model-shard1`

## Usage Guide

### Registration Process

1. **Navigate to Registration**
   - Click "Register New User" on the home page
   - Fill in your personal information (username, email, full name)

2. **Face Capture**
   - Allow camera permissions when prompted
   - Position your face in the center of the frame
   - Ensure good lighting and remove glasses if possible
   - Click "Capture Face" when the system detects your face

3. **Confirmation**
   - Review your registration details
   - Your face data is now securely stored locally

### Authentication Process

1. **Navigate to Login**
   - Click "Login with Face" on the home page
   - Allow camera permissions if not already granted

2. **Face Authentication**
   - Click "Start Authentication"
   - Look directly at the camera
   - Stay still during the authentication process
   - The system will compare your face with stored data

3. **Access Dashboard**
   - Upon successful authentication, you'll be redirected to your dashboard
   - View your profile, authentication history, and system settings

### Dashboard Features

- **Profile Information**: View and manage your account details
- **Authentication History**: See recent login attempts and their results
- **Security Settings**: Manage face recognition settings and data
- **System Information**: Check browser compatibility and storage usage
- **Quick Actions**: Test face recognition, export data, or clear all data

## Browser Compatibility

### Supported Browsers
- Chrome 60+
- Firefox 55+
- Safari 11+
- Edge 79+

### Required Features
- WebRTC getUserMedia API
- IndexedDB
- ES6+ JavaScript support
- Canvas API
- Promises/async-await

## Security Considerations

### Data Protection
- All biometric data is stored locally on the user's device
- Face descriptors are encoded using base64 encryption
- No data is transmitted to external servers
- Users can export or delete their data at any time

### Privacy Features
- Camera access is only requested when needed
- Users have full control over their biometric data
- Clear consent process during registration
- Transparent data usage policies

## Troubleshooting

### Common Issues

**Camera Not Working**
- Ensure HTTPS connection
- Check browser permissions
- Verify camera is not being used by another application
- Try refreshing the page

**Face Detection Issues**
- Improve lighting conditions
- Remove glasses or hats
- Ensure face is centered in frame
- Check if face-api.js models loaded correctly

**Storage Problems**
- Verify IndexedDB is supported
- Check available storage space
- Clear browser cache if needed

**Performance Issues**
- Close other browser tabs
- Ensure adequate system resources
- Try using a different browser

### Error Messages
The system provides detailed error messages and suggestions for resolution. Check the browser console for additional debugging information.

## Development

### Customization
- Modify `styles/main.css` for visual customization
- Adjust recognition threshold in `js/faceAuth.js`
- Add new features by extending the existing modules

### Testing
- Use the "Test Face Recognition" feature in the dashboard
- Check browser console for detailed logs
- Verify camera and storage functionality

## Limitations

- Requires modern browser with camera support
- Performance depends on device capabilities
- Face recognition accuracy varies with lighting conditions
- Local storage only (no cloud synchronization)

## Future Enhancements

- Multi-factor authentication
- Cloud storage integration
- Advanced face recognition algorithms
- Mobile app version
- Admin panel for user management

## License

This project is provided as-is for educational and demonstration purposes. Please ensure compliance with local privacy and biometric data regulations when using in production environments.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review browser console for error messages
3. Ensure all prerequisites are met
4. Test with different browsers if needed

---

**Note**: This is a proof-of-concept implementation. For production use, consider additional security measures, server-side validation, and compliance with biometric data regulations.
