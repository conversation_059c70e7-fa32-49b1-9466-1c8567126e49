<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Face Detection Test - Facial Authentication</title>
    <link rel="stylesheet" href="styles/main.css">
    <script defer src="https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Face Detection Test</h1>
            <p class="subtitle">Debug and test face detection functionality</p>
        </header>

        <main class="main-content">
            <div class="test-container">
                <div class="status-section">
                    <h3>System Status</h3>
                    <div class="status-grid">
                        <div class="status-item">
                            <span class="status-label">Camera:</span>
                            <span class="status-value" id="cameraStatus">Checking...</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Face API:</span>
                            <span class="status-value" id="faceApiStatus">Loading...</span>
                        </div>
                        <div class="status-item">
                            <span class="status-label">Models:</span>
                            <span class="status-value" id="modelsStatus">Loading...</span>
                        </div>
                    </div>
                </div>

                <div class="camera-test-section">
                    <h3>Camera Test</h3>
                    <div class="camera-container">
                        <video id="video" autoplay muted playsinline style="width: 100%; max-width: 640px;"></video>
                        <canvas id="overlay" class="overlay-canvas"></canvas>
                    </div>
                    
                    <div class="controls">
                        <button class="btn btn-primary" id="startBtn" onclick="startTest()">Start Camera Test</button>
                        <button class="btn btn-secondary" id="stopBtn" onclick="stopTest()" disabled>Stop Test</button>
                    </div>
                </div>

                <div class="detection-info">
                    <h3>Detection Information</h3>
                    <div id="detectionResults">
                        <p>Click "Start Camera Test" to begin face detection testing.</p>
                    </div>
                </div>

                <div class="debug-logs">
                    <h3>Debug Logs</h3>
                    <div id="debugLogs" style="background: #f0f0f0; padding: 1rem; border-radius: 8px; max-height: 200px; overflow-y: auto; font-family: monospace; font-size: 0.9rem;">
                        <p>Debug information will appear here...</p>
                    </div>
                    <button class="btn btn-text" onclick="clearLogs()">Clear Logs</button>
                </div>

                <div class="troubleshooting">
                    <h3>Troubleshooting Tips</h3>
                    <div class="tips-grid">
                        <div class="tip-card">
                            <h4>Camera Issues</h4>
                            <ul>
                                <li>Allow camera permissions</li>
                                <li>Close other apps using camera</li>
                                <li>Try refreshing the page</li>
                                <li>Check if camera is working in other apps</li>
                            </ul>
                        </div>
                        <div class="tip-card">
                            <h4>Face Detection Issues</h4>
                            <ul>
                                <li>Ensure good lighting</li>
                                <li>Face the camera directly</li>
                                <li>Remove glasses or hats</li>
                                <li>Stay still during detection</li>
                                <li>Move closer to the camera</li>
                            </ul>
                        </div>
                        <div class="tip-card">
                            <h4>Model Loading Issues</h4>
                            <ul>
                                <li>Check internet connection</li>
                                <li>Disable ad blockers temporarily</li>
                                <li>Try a different browser</li>
                                <li>Clear browser cache</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="back-button">
                    <button class="btn btn-text" onclick="navigateToHome()">← Back to Home</button>
                </div>
            </div>
        </main>
    </div>

    <script src="js/utils.js"></script>
    <script>
        let video, canvas, context;
        let isTestRunning = false;
        let detectionInterval;

        // Debug logging function
        function debugLog(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('debugLogs');
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[Face Test] ${message}`);
        }

        function clearLogs() {
            document.getElementById('debugLogs').innerHTML = '<p>Debug logs cleared...</p>';
        }

        // Initialize test page
        async function initTest() {
            debugLog('Initializing face detection test...');
            
            video = document.getElementById('video');
            canvas = document.getElementById('overlay');
            context = canvas.getContext('2d');

            // Check camera support
            if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                Utils.updateStatus('cameraStatus', 'ready', 'Supported');
                debugLog('Camera API supported');
            } else {
                Utils.updateStatus('cameraStatus', 'error', 'Not supported');
                debugLog('ERROR: Camera API not supported');
                return;
            }

            // Initialize face-api
            try {
                debugLog('Loading face-api.js models...');
                Utils.updateStatus('faceApiStatus', 'loading', 'Loading...');
                
                const modelBaseUrl = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights';
                
                await Promise.all([
                    faceapi.nets.tinyFaceDetector.loadFromUri(modelBaseUrl),
                    faceapi.nets.faceLandmark68Net.loadFromUri(modelBaseUrl),
                    faceapi.nets.faceRecognitionNet.loadFromUri(modelBaseUrl)
                ]);

                Utils.updateStatus('faceApiStatus', 'ready', 'Ready');
                Utils.updateStatus('modelsStatus', 'ready', 'Loaded');
                debugLog('Face-api.js models loaded successfully');
                
            } catch (error) {
                Utils.updateStatus('faceApiStatus', 'error', 'Failed');
                Utils.updateStatus('modelsStatus', 'error', 'Failed');
                debugLog(`ERROR: Failed to load models - ${error.message}`);
            }
        }

        async function startTest() {
            if (isTestRunning) return;
            
            try {
                debugLog('Starting camera test...');
                
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: { width: 640, height: 480, facingMode: 'user' },
                    audio: false
                });
                
                video.srcObject = stream;
                isTestRunning = true;
                
                document.getElementById('startBtn').disabled = true;
                document.getElementById('stopBtn').disabled = false;
                
                video.onloadedmetadata = () => {
                    video.play();
                    setupCanvas();
                    startDetection();
                    debugLog('Camera started successfully');
                };
                
            } catch (error) {
                debugLog(`ERROR: Camera access failed - ${error.message}`);
                Utils.updateStatus('cameraStatus', 'error', 'Access denied');
            }
        }

        function setupCanvas() {
            canvas.width = video.videoWidth;
            canvas.height = video.videoHeight;
            canvas.style.width = video.offsetWidth + 'px';
            canvas.style.height = video.offsetHeight + 'px';
        }

        async function startDetection() {
            if (!isTestRunning) return;
            
            try {
                const detections = await faceapi
                    .detectAllFaces(video, new faceapi.TinyFaceDetectorOptions({
                        inputSize: 416,
                        scoreThreshold: 0.3
                    }))
                    .withFaceLandmarks()
                    .withFaceDescriptors();

                // Clear canvas
                context.clearRect(0, 0, canvas.width, canvas.height);
                
                // Update detection info
                updateDetectionInfo(detections);
                
                // Draw detection results
                if (detections.length > 0) {
                    detections.forEach(detection => {
                        const box = detection.detection.box;
                        const confidence = detection.detection.score;
                        
                        // Draw face box
                        context.strokeStyle = confidence > 0.6 ? '#00ff00' : '#ffaa00';
                        context.lineWidth = 2;
                        context.strokeRect(box.x, box.y, box.width, box.height);
                        
                        // Draw confidence
                        context.fillStyle = context.strokeStyle;
                        context.font = '16px Arial';
                        context.fillText(`${Math.round(confidence * 100)}%`, box.x, box.y - 5);
                        
                        // Draw landmarks
                        if (detection.landmarks) {
                            context.fillStyle = '#ff0000';
                            detection.landmarks.positions.forEach(point => {
                                context.beginPath();
                                context.arc(point.x, point.y, 1, 0, 2 * Math.PI);
                                context.fill();
                            });
                        }
                    });
                }
                
            } catch (error) {
                debugLog(`ERROR: Detection failed - ${error.message}`);
            }
            
            if (isTestRunning) {
                setTimeout(startDetection, 100);
            }
        }

        function updateDetectionInfo(detections) {
            const resultsDiv = document.getElementById('detectionResults');
            
            if (detections.length === 0) {
                resultsDiv.innerHTML = `
                    <p><strong>Status:</strong> No faces detected</p>
                    <p><strong>Tip:</strong> Make sure your face is visible and well-lit</p>
                `;
            } else {
                const detection = detections[0];
                const confidence = Math.round(detection.detection.score * 100);
                const box = detection.detection.box;
                
                resultsDiv.innerHTML = `
                    <p><strong>Faces detected:</strong> ${detections.length}</p>
                    <p><strong>Confidence:</strong> ${confidence}%</p>
                    <p><strong>Face size:</strong> ${Math.round(box.width)} x ${Math.round(box.height)} pixels</p>
                    <p><strong>Position:</strong> (${Math.round(box.x)}, ${Math.round(box.y)})</p>
                    <p><strong>Quality:</strong> ${confidence > 60 ? '✅ Good' : confidence > 40 ? '⚠️ Fair' : '❌ Poor'}</p>
                `;
                
                if (confidence > 50) {
                    debugLog(`Face detected with ${confidence}% confidence`);
                }
            }
        }

        function stopTest() {
            isTestRunning = false;
            
            if (video.srcObject) {
                video.srcObject.getTracks().forEach(track => track.stop());
                video.srcObject = null;
            }
            
            context.clearRect(0, 0, canvas.width, canvas.height);
            
            document.getElementById('startBtn').disabled = false;
            document.getElementById('stopBtn').disabled = true;
            
            document.getElementById('detectionResults').innerHTML = '<p>Test stopped. Click "Start Camera Test" to try again.</p>';
            
            debugLog('Camera test stopped');
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initTest);
    </script>
</body>
</html>
