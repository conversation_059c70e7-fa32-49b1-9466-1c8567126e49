/**
 * IndexedDB storage module for facial authentication system
 * Handles secure storage of user data and facial descriptors
 */

class FaceAuthStorage {
    constructor() {
        this.dbName = 'FaceAuthDB';
        this.dbVersion = 1;
        this.db = null;
        this.stores = {
            users: 'users',
            sessions: 'sessions',
            settings: 'settings'
        };
    }

    /**
     * Initialize the database
     */
    async init() {
        return new Promise((resolve, reject) => {
            if (!window.indexedDB) {
                reject(new Error('IndexedDB is not supported'));
                return;
            }

            const request = indexedDB.open(this.dbName, this.dbVersion);

            request.onerror = () => {
                reject(new Error('Failed to open database'));
            };

            request.onsuccess = (event) => {
                this.db = event.target.result;
                resolve(this.db);
            };

            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                this.createStores(db);
            };
        });
    }

    /**
     * Create object stores
     */
    createStores(db) {
        // Users store
        if (!db.objectStoreNames.contains(this.stores.users)) {
            const userStore = db.createObjectStore(this.stores.users, { keyPath: 'id' });
            userStore.createIndex('username', 'username', { unique: true });
            userStore.createIndex('email', 'email', { unique: true });
        }

        // Sessions store
        if (!db.objectStoreNames.contains(this.stores.sessions)) {
            const sessionStore = db.createObjectStore(this.stores.sessions, { keyPath: 'id' });
            sessionStore.createIndex('userId', 'userId', { unique: false });
            sessionStore.createIndex('timestamp', 'timestamp', { unique: false });
        }

        // Settings store
        if (!db.objectStoreNames.contains(this.stores.settings)) {
            db.createObjectStore(this.stores.settings, { keyPath: 'key' });
        }
    }

    /**
     * Save user data with facial descriptors
     */
    async saveUser(userData) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        const user = {
            id: Utils.generateId(),
            username: userData.username,
            email: userData.email,
            fullName: userData.fullName,
            faceDescriptor: Utils.encodeData(userData.faceDescriptor),
            registrationDate: new Date().toISOString(),
            lastLogin: null,
            isActive: true
        };

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.users], 'readwrite');
            const store = transaction.objectStore(this.stores.users);
            const request = store.add(user);

            request.onsuccess = () => {
                resolve(user);
            };

            request.onerror = () => {
                reject(new Error('Failed to save user data'));
            };
        });
    }

    /**
     * Get user by username
     */
    async getUserByUsername(username) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.users], 'readonly');
            const store = transaction.objectStore(this.stores.users);
            const index = store.index('username');
            const request = index.get(username);

            request.onsuccess = () => {
                const user = request.result;
                if (user && user.faceDescriptor) {
                    user.faceDescriptor = Utils.decodeData(user.faceDescriptor);
                }
                resolve(user);
            };

            request.onerror = () => {
                reject(new Error('Failed to retrieve user'));
            };
        });
    }

    /**
     * Get user by ID
     */
    async getUserById(userId) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.users], 'readonly');
            const store = transaction.objectStore(this.stores.users);
            const request = store.get(userId);

            request.onsuccess = () => {
                const user = request.result;
                if (user && user.faceDescriptor) {
                    user.faceDescriptor = Utils.decodeData(user.faceDescriptor);
                }
                resolve(user);
            };

            request.onerror = () => {
                reject(new Error('Failed to retrieve user'));
            };
        });
    }

    /**
     * Get all users
     */
    async getAllUsers() {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.users], 'readonly');
            const store = transaction.objectStore(this.stores.users);
            const request = store.getAll();

            request.onsuccess = () => {
                const users = request.result.map(user => {
                    if (user.faceDescriptor) {
                        user.faceDescriptor = Utils.decodeData(user.faceDescriptor);
                    }
                    return user;
                });
                resolve(users);
            };

            request.onerror = () => {
                reject(new Error('Failed to retrieve users'));
            };
        });
    }

    /**
     * Update user data
     */
    async updateUser(userId, updates) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.users], 'readwrite');
            const store = transaction.objectStore(this.stores.users);
            const getRequest = store.get(userId);

            getRequest.onsuccess = () => {
                const user = getRequest.result;
                if (!user) {
                    reject(new Error('User not found'));
                    return;
                }

                // Update user data
                Object.assign(user, updates);
                
                // Encode face descriptor if provided
                if (updates.faceDescriptor) {
                    user.faceDescriptor = Utils.encodeData(updates.faceDescriptor);
                }

                const putRequest = store.put(user);
                putRequest.onsuccess = () => resolve(user);
                putRequest.onerror = () => reject(new Error('Failed to update user'));
            };

            getRequest.onerror = () => {
                reject(new Error('Failed to retrieve user for update'));
            };
        });
    }

    /**
     * Delete user
     */
    async deleteUser(userId) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.users], 'readwrite');
            const store = transaction.objectStore(this.stores.users);
            const request = store.delete(userId);

            request.onsuccess = () => {
                resolve(true);
            };

            request.onerror = () => {
                reject(new Error('Failed to delete user'));
            };
        });
    }

    /**
     * Save authentication session
     */
    async saveSession(userId, authResult) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        const session = {
            id: Utils.generateId(),
            userId: userId,
            timestamp: new Date().toISOString(),
            success: authResult.success,
            confidence: authResult.confidence || null,
            method: 'facial_recognition',
            userAgent: navigator.userAgent,
            ipAddress: 'local' // In a real app, you'd get this from server
        };

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.sessions], 'readwrite');
            const store = transaction.objectStore(this.stores.sessions);
            const request = store.add(session);

            request.onsuccess = () => {
                resolve(session);
            };

            request.onerror = () => {
                reject(new Error('Failed to save session'));
            };
        });
    }

    /**
     * Get user sessions
     */
    async getUserSessions(userId, limit = 10) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.sessions], 'readonly');
            const store = transaction.objectStore(this.stores.sessions);
            const index = store.index('userId');
            const request = index.getAll(userId);

            request.onsuccess = () => {
                const sessions = request.result
                    .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
                    .slice(0, limit);
                resolve(sessions);
            };

            request.onerror = () => {
                reject(new Error('Failed to retrieve sessions'));
            };
        });
    }

    /**
     * Save application settings
     */
    async saveSetting(key, value) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        const setting = { key, value, timestamp: new Date().toISOString() };

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.settings], 'readwrite');
            const store = transaction.objectStore(this.stores.settings);
            const request = store.put(setting);

            request.onsuccess = () => {
                resolve(setting);
            };

            request.onerror = () => {
                reject(new Error('Failed to save setting'));
            };
        });
    }

    /**
     * Get application setting
     */
    async getSetting(key) {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction([this.stores.settings], 'readonly');
            const store = transaction.objectStore(this.stores.settings);
            const request = store.get(key);

            request.onsuccess = () => {
                resolve(request.result?.value || null);
            };

            request.onerror = () => {
                reject(new Error('Failed to retrieve setting'));
            };
        });
    }

    /**
     * Clear all data
     */
    async clearAllData() {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        const storeNames = Object.values(this.stores);
        
        return new Promise((resolve, reject) => {
            const transaction = this.db.transaction(storeNames, 'readwrite');
            let completed = 0;
            
            storeNames.forEach(storeName => {
                const store = transaction.objectStore(storeName);
                const request = store.clear();
                
                request.onsuccess = () => {
                    completed++;
                    if (completed === storeNames.length) {
                        resolve(true);
                    }
                };
                
                request.onerror = () => {
                    reject(new Error(`Failed to clear ${storeName} store`));
                };
            });
        });
    }

    /**
     * Get storage usage statistics
     */
    async getStorageStats() {
        if (!this.db) {
            throw new Error('Database not initialized');
        }

        const stats = {
            users: 0,
            sessions: 0,
            settings: 0
        };

        const storeNames = Object.values(this.stores);
        
        for (const storeName of storeNames) {
            const transaction = this.db.transaction([storeName], 'readonly');
            const store = transaction.objectStore(storeName);
            const countRequest = store.count();
            
            await new Promise((resolve) => {
                countRequest.onsuccess = () => {
                    stats[storeName] = countRequest.result;
                    resolve();
                };
            });
        }

        return stats;
    }
}

// Create global storage instance
const storage = new FaceAuthStorage();

// Export for use in other modules
window.Storage = storage;
