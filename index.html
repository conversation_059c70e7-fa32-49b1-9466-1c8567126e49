<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facial Authentication System</title>
    <link rel="stylesheet" href="styles/main.css">
    <script defer src="https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/dist/face-api.min.js"></script>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>Facial Authentication System</h1>
            <p class="subtitle">Secure biometric authentication for the modern web</p>
        </header>

        <main class="main-content">
            <div class="welcome-section">
                <h2>Welcome to Secure Face Authentication</h2>
                <p>Experience the future of web authentication with our advanced facial recognition technology. 
                   Register your face once and enjoy seamless, secure access to your account.</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">🔒</div>
                    <h3>Secure</h3>
                    <p>Your biometric data is encrypted and stored locally on your device</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">⚡</div>
                    <h3>Fast</h3>
                    <p>Instant authentication in under 2 seconds</p>
                </div>
                <div class="feature-card">
                    <div class="feature-icon">📱</div>
                    <h3>Universal</h3>
                    <p>Works on desktop and mobile browsers</p>
                </div>
            </div>

            <div class="action-buttons">
                <button class="btn btn-primary" onclick="navigateToRegister()">
                    <span class="btn-icon">👤</span>
                    Register New User
                </button>
                <button class="btn btn-secondary" onclick="navigateToLogin()">
                    <span class="btn-icon">🔑</span>
                    Login with Face
                </button>
                <button class="btn btn-text" onclick="window.location.href='test.html'">
                    <span class="btn-icon">🧪</span>
                    Test Face Detection
                </button>
            </div>

            <div class="system-status" id="systemStatus">
                <div class="status-item">
                    <span class="status-label">Camera:</span>
                    <span class="status-value" id="cameraStatus">Checking...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Face Detection:</span>
                    <span class="status-value" id="faceApiStatus">Loading...</span>
                </div>
                <div class="status-item">
                    <span class="status-label">Storage:</span>
                    <span class="status-value" id="storageStatus">Checking...</span>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 Facial Authentication System. Built with vanilla JavaScript.</p>
        </footer>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/camera.js"></script>
    <script src="js/faceAuth.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
