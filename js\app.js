/**
 * Main application logic for facial authentication system
 * Handles page-specific functionality and user interactions
 */

// Global application state
let currentUser = null;
let registrationData = {};
let authenticationInProgress = false;

/**
 * Initialize application based on current page
 */
document.addEventListener('DOMContentLoaded', async () => {
    try {
        // Initialize storage
        await Storage.init();
        
        // Initialize face authentication
        await FaceAuth.init();
        
        // Check which page we're on and initialize accordingly
        const path = window.location.pathname;
        const page = path.substring(path.lastIndexOf('/') + 1);
        
        switch (page) {
            case 'index.html':
            case '':
                await initHomePage();
                break;
            case 'register.html':
                await initRegisterPage();
                break;
            case 'login.html':
                await initLoginPage();
                break;
            case 'dashboard.html':
                await initDashboardPage();
                break;
        }
    } catch (error) {
        console.error('Application initialization failed:', error);
        Utils.showError('Failed to initialize application: ' + error.message);
    }
});

/**
 * Initialize home page
 */
async function initHomePage() {
    try {
        // Check system capabilities
        await checkSystemCapabilities();
        
        // Check if user is already logged in
        const sessionUser = await getCurrentSession();
        if (sessionUser) {
            Utils.setElementText('userGreeting', `Welcome back, ${sessionUser.fullName}!`);
            // Could show a "Continue to Dashboard" button
        }
    } catch (error) {
        console.error('Home page initialization failed:', error);
    }
}

/**
 * Initialize registration page
 */
async function initRegisterPage() {
    try {
        // Set up form validation
        setupFormValidation();
        
        // Initialize camera when moving to face capture step
        // (Camera will be initialized in proceedToFaceCapture function)
    } catch (error) {
        console.error('Register page initialization failed:', error);
    }
}

/**
 * Initialize login page
 */
async function initLoginPage() {
    try {
        // Check if there are any registered users
        const users = await Storage.getAllUsers();
        if (users.length === 0) {
            Utils.showError('No registered users found. Please register first.');
            setTimeout(() => navigateToRegister(), 3000);
            return;
        }
        
        // Initialize camera
        await Camera.init();
        Utils.updateStatus('cameraStatus', 'ready', 'Ready');
        
        // Update UI
        Utils.setElementText('statusText', 'Camera ready. Click "Start Authentication" to begin.');
        Utils.updateDetectionStatus('ready', 'Ready to authenticate');
        
    } catch (error) {
        console.error('Login page initialization failed:', error);
        Utils.updateStatus('cameraStatus', 'error', 'Failed');
        Utils.setElementText('statusText', 'Camera initialization failed: ' + error.message);
    }
}

/**
 * Initialize dashboard page
 */
async function initDashboardPage() {
    try {
        // Check if user is authenticated
        currentUser = await getCurrentSession();
        if (!currentUser) {
            Utils.showError('Please log in to access the dashboard.');
            navigateToLogin();
            return;
        }
        
        // Load user data
        await loadDashboardData();
        
    } catch (error) {
        console.error('Dashboard initialization failed:', error);
        Utils.showError('Failed to load dashboard: ' + error.message);
    }
}

/**
 * Check system capabilities
 */
async function checkSystemCapabilities() {
    // Check camera support
    if (Utils.checkCameraSupport()) {
        Utils.updateStatus('cameraStatus', 'ready', 'Supported');
    } else {
        Utils.updateStatus('cameraStatus', 'error', 'Not supported');
    }
    
    // Check IndexedDB support
    if (Utils.checkIndexedDBSupport()) {
        Utils.updateStatus('storageStatus', 'ready', 'Available');
    } else {
        Utils.updateStatus('storageStatus', 'error', 'Not available');
    }
}

/**
 * Registration flow functions
 */
function proceedToFaceCapture() {
    if (!Utils.validateForm('userForm')) {
        Utils.showError('Please fill in all required fields correctly.');
        return;
    }
    
    // Store user form data
    const form = document.getElementById('userForm');
    const formData = new FormData(form);
    registrationData = {
        username: formData.get('username'),
        email: formData.get('email'),
        fullName: formData.get('fullName')
    };
    
    // Move to next step
    Utils.hideElement('userInfoStep');
    Utils.showElement('faceCaptureStep');
    updateStepIndicator(2);
    
    // Initialize camera
    initializeCameraForCapture();
}

async function initializeCameraForCapture() {
    try {
        Utils.updateDetectionStatus('loading', 'Initializing camera...');
        
        await Camera.init();
        
        // Start face detection
        FaceAuth.startDetection(Camera.video, (detections) => {
            FaceAuth.drawDetections(detections, Camera.canvas);
            
            const status = FaceAuth.getDetectionStatus();
            updateCaptureUI(status);
        });
        
        Utils.updateDetectionStatus('detecting', 'Position your face in the frame');
        
    } catch (error) {
        console.error('Camera initialization failed:', error);
        Utils.updateDetectionStatus('error', 'Camera failed: ' + error.message);
    }
}

function updateCaptureUI(status) {
    const captureBtn = document.getElementById('captureBtn');
    
    switch (status.status) {
        case 'face_detected':
            Utils.updateDetectionStatus('ready', `Face detected (${Math.round(status.confidence * 100)}%)`);
            captureBtn.disabled = false;
            break;
        case 'no_face':
            Utils.updateDetectionStatus('detecting', 'No face detected - position your face in the frame');
            captureBtn.disabled = true;
            break;
        case 'multiple_faces':
            Utils.updateDetectionStatus('error', 'Multiple faces detected - ensure only one person is visible');
            captureBtn.disabled = true;
            break;
        default:
            captureBtn.disabled = true;
    }
}

async function captureFace() {
    try {
        Utils.showLoading('Processing face data...');
        
        // Validate face quality
        const quality = await FaceAuth.validateFaceQuality(Camera.video);
        if (!quality.valid) {
            Utils.hideLoading();
            Utils.showError(`Face quality check failed: ${quality.message}. ${quality.suggestions.join(', ')}`);
            return;
        }
        
        // Extract face descriptor
        const faceData = await FaceAuth.extractFaceDescriptor(Camera.video);
        registrationData.faceDescriptor = faceData.descriptor;
        
        // Save user to database
        const user = await Storage.saveUser(registrationData);
        currentUser = user;
        
        // Stop camera and detection
        FaceAuth.stopDetection();
        Camera.stopCamera();
        
        Utils.hideLoading();
        
        // Move to confirmation step
        Utils.hideElement('faceCaptureStep');
        Utils.showElement('confirmationStep');
        updateStepIndicator(3);
        
        // Show user summary
        showRegistrationSummary(user);
        
    } catch (error) {
        Utils.hideLoading();
        console.error('Face capture failed:', error);
        Utils.showError('Face capture failed: ' + error.message);
    }
}

function retryCapture() {
    // Reset camera and detection
    FaceAuth.stopDetection();
    Camera.clearCanvas();
    initializeCameraForCapture();
}

function showRegistrationSummary(user) {
    const summary = document.getElementById('userSummary');
    if (summary) {
        summary.innerHTML = `
            <div class="info-item">
                <span class="info-label">Username:</span>
                <span class="info-value">${user.username}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Full Name:</span>
                <span class="info-value">${user.fullName}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Email:</span>
                <span class="info-value">${user.email}</span>
            </div>
            <div class="info-item">
                <span class="info-label">Registration Date:</span>
                <span class="info-value">${Utils.formatDate(new Date(user.registrationDate))}</span>
            </div>
        `;
    }
}

function updateStepIndicator(activeStep) {
    for (let i = 1; i <= 3; i++) {
        const step = document.getElementById(`step${i}`);
        if (step) {
            if (i === activeStep) {
                step.classList.add('active');
            } else {
                step.classList.remove('active');
            }
        }
    }
}

/**
 * Login flow functions
 */
async function startAuthentication() {
    if (authenticationInProgress) return;
    
    try {
        authenticationInProgress = true;
        Utils.setElementText('statusText', 'Authenticating...');
        Utils.updateProgress(0);
        
        const startBtn = document.getElementById('startAuthBtn');
        const retryBtn = document.getElementById('retryBtn');
        
        if (startBtn) startBtn.style.display = 'none';
        if (retryBtn) retryBtn.style.display = 'none';
        
        // Get all registered users
        const users = await Storage.getAllUsers();
        
        // Start authentication process
        let progress = 0;
        const progressInterval = setInterval(() => {
            progress += 10;
            Utils.updateProgress(Math.min(progress, 90));
        }, 200);
        
        // Perform authentication
        const result = await FaceAuth.authenticateUser(Camera.video, users);
        
        clearInterval(progressInterval);
        Utils.updateProgress(100);
        
        // Save authentication session
        if (result.success) {
            await Storage.saveSession(result.user.id, result);
            await Storage.updateUser(result.user.id, { lastLogin: new Date().toISOString() });
        } else {
            await Storage.saveSession('unknown', result);
        }
        
        // Show result
        showAuthenticationResult(result);
        
    } catch (error) {
        console.error('Authentication failed:', error);
        showAuthenticationResult({
            success: false,
            message: 'Authentication failed: ' + error.message
        });
    } finally {
        authenticationInProgress = false;
    }
}

function retryAuthentication() {
    const startBtn = document.getElementById('startAuthBtn');
    const retryBtn = document.getElementById('retryBtn');
    
    if (startBtn) startBtn.style.display = 'inline-flex';
    if (retryBtn) retryBtn.style.display = 'none';
    
    Utils.setElementText('statusText', 'Ready to authenticate. Click "Start Authentication" to try again.');
    Utils.updateProgress(0);
    
    Camera.clearCanvas();
}

function showAuthenticationResult(result) {
    const modal = document.getElementById('resultModal');
    const title = document.getElementById('modalTitle');
    const icon = document.getElementById('resultIcon');
    const message = document.getElementById('resultMessage');
    const userInfo = document.getElementById('userInfo');
    const actionBtn = document.getElementById('modalActionBtn');
    
    if (result.success) {
        title.textContent = 'Authentication Successful';
        icon.textContent = '✅';
        icon.style.color = '#38a169';
        message.textContent = result.message;
        
        if (userInfo && result.user) {
            userInfo.style.display = 'block';
            userInfo.innerHTML = `
                <h4>Welcome back!</h4>
                <p><strong>Name:</strong> ${result.user.fullName}</p>
                <p><strong>Username:</strong> ${result.user.username}</p>
                <p><strong>Confidence:</strong> ${Math.round(result.confidence * 100)}%</p>
            `;
        }
        
        actionBtn.textContent = 'Go to Dashboard';
        actionBtn.onclick = () => {
            currentUser = result.user;
            navigateToDashboard();
        };
    } else {
        title.textContent = 'Authentication Failed';
        icon.textContent = '❌';
        icon.style.color = '#e53e3e';
        message.textContent = result.message;
        
        if (userInfo) {
            userInfo.style.display = 'none';
        }
        
        actionBtn.textContent = 'Try Again';
        actionBtn.onclick = () => {
            closeModal();
            retryAuthentication();
        };
    }
    
    Utils.showElement('resultModal');
}

function closeModal() {
    Utils.hideElement('resultModal');
}

function handleModalAction() {
    // This function is called by the modal action button
    // The actual action is set in showAuthenticationResult
}

/**
 * Dashboard functions
 */
async function loadDashboardData() {
    try {
        // Update user greeting
        Utils.setElementText('userGreeting', `Welcome, ${currentUser.fullName}!`);
        
        // Load profile information
        Utils.setElementText('displayUsername', currentUser.username);
        Utils.setElementText('displayFullName', currentUser.fullName);
        Utils.setElementText('displayEmail', currentUser.email);
        Utils.setElementText('displayRegDate', Utils.formatDate(new Date(currentUser.registrationDate)));
        
        // Load authentication history
        const sessions = await Storage.getUserSessions(currentUser.id, 5);
        if (sessions.length > 0) {
            const lastSession = sessions[0];
            Utils.setElementText('lastLoginTime', Utils.formatRelativeTime(new Date(lastSession.timestamp)));
        }
        
        // Load system information
        Utils.setElementText('browserInfo', Utils.getBrowserInfo());
        Utils.setElementText('cameraSupport', Utils.checkCameraSupport() ? 'Available' : 'Not Available');
        
        // Load storage statistics
        const stats = await Storage.getStorageStats();
        Utils.setElementText('storageUsed', `${stats.users} users, ${stats.sessions} sessions`);
        
    } catch (error) {
        console.error('Failed to load dashboard data:', error);
        Utils.showError('Failed to load some dashboard data');
    }
}

/**
 * Session management
 */
async function getCurrentSession() {
    try {
        const userId = localStorage.getItem('currentUserId');
        if (!userId) return null;
        
        const user = await Storage.getUserById(userId);
        return user;
    } catch (error) {
        console.error('Failed to get current session:', error);
        return null;
    }
}

function setCurrentSession(user) {
    localStorage.setItem('currentUserId', user.id);
    currentUser = user;
}

function clearCurrentSession() {
    localStorage.removeItem('currentUserId');
    currentUser = null;
}

function logout() {
    clearCurrentSession();
    navigateToHome();
}

/**
 * Form validation setup
 */
function setupFormValidation() {
    const form = document.getElementById('userForm');
    if (!form) return;
    
    const inputs = form.querySelectorAll('input');
    inputs.forEach(input => {
        input.addEventListener('blur', () => {
            validateInput(input);
        });
        
        input.addEventListener('input', () => {
            // Clear error styling on input
            input.style.borderColor = '#e2e8f0';
        });
    });
}

function validateInput(input) {
    let isValid = true;
    
    if (input.hasAttribute('required') && !input.value.trim()) {
        isValid = false;
    }
    
    if (input.type === 'email' && input.value && !Utils.validateEmail(input.value)) {
        isValid = false;
    }
    
    if (input.name === 'username' && input.value && !Utils.validateUsername(input.value)) {
        isValid = false;
    }
    
    input.style.borderColor = isValid ? '#e2e8f0' : '#e53e3e';
    return isValid;
}

/**
 * Dashboard action functions
 */
function editProfile() {
    Utils.showError('Profile editing feature coming soon!');
}

function viewFullHistory() {
    Utils.showError('Full history view coming soon!');
}

async function updateFaceData() {
    if (!confirm('This will replace your current face data. Continue?')) {
        return;
    }

    try {
        // Navigate to registration with update mode
        localStorage.setItem('updateMode', 'true');
        navigateToRegister();
    } catch (error) {
        Utils.showError('Failed to start face data update: ' + error.message);
    }
}

async function deleteAccount() {
    showConfirmModal(
        'Delete Account',
        'Are you sure you want to delete your account? This action cannot be undone and will remove all your data.',
        async () => {
            try {
                await Storage.deleteUser(currentUser.id);
                clearCurrentSession();
                Utils.showSuccess('Account deleted successfully');
                setTimeout(() => navigateToHome(), 2000);
            } catch (error) {
                Utils.showError('Failed to delete account: ' + error.message);
            }
        }
    );
}

async function testAuthentication() {
    try {
        // Initialize camera for testing
        await Camera.init();

        // Show a simple test modal
        const modal = document.getElementById('resultModal');
        const title = document.getElementById('modalTitle');
        const message = document.getElementById('resultMessage');
        const actionBtn = document.getElementById('modalActionBtn');

        title.textContent = 'Face Recognition Test';
        message.textContent = 'Testing face recognition capabilities...';
        actionBtn.textContent = 'Close';
        actionBtn.onclick = () => {
            Camera.stopCamera();
            closeModal();
        };

        Utils.showElement('resultModal');

        // Perform a quick face detection test
        setTimeout(async () => {
            try {
                const detections = await FaceAuth.detectFaces(Camera.video);
                if (detections.length > 0) {
                    message.textContent = `✅ Face detection working! Detected ${detections.length} face(s) with ${Math.round(detections[0].detection.score * 100)}% confidence.`;
                } else {
                    message.textContent = '⚠️ No faces detected. Please ensure you are visible to the camera.';
                }
            } catch (error) {
                message.textContent = '❌ Face detection test failed: ' + error.message;
            }
        }, 2000);

    } catch (error) {
        Utils.showError('Failed to start face recognition test: ' + error.message);
    }
}

async function downloadData() {
    try {
        const userData = await Storage.getUserById(currentUser.id);
        const sessions = await Storage.getUserSessions(currentUser.id);

        const exportData = {
            user: {
                username: userData.username,
                fullName: userData.fullName,
                email: userData.email,
                registrationDate: userData.registrationDate,
                lastLogin: userData.lastLogin
            },
            sessions: sessions.map(session => ({
                timestamp: session.timestamp,
                success: session.success,
                confidence: session.confidence,
                method: session.method
            })),
            exportDate: new Date().toISOString()
        };

        Utils.downloadData(exportData, `face-auth-data-${userData.username}.json`);
        Utils.showSuccess('Data exported successfully');
    } catch (error) {
        Utils.showError('Failed to export data: ' + error.message);
    }
}

async function clearData() {
    showConfirmModal(
        'Clear All Data',
        'This will remove all users, sessions, and settings. This action cannot be undone.',
        async () => {
            try {
                await Storage.clearAllData();
                clearCurrentSession();
                Utils.showSuccess('All data cleared successfully');
                setTimeout(() => navigateToHome(), 2000);
            } catch (error) {
                Utils.showError('Failed to clear data: ' + error.message);
            }
        }
    );
}

/**
 * Modal functions
 */
function showConfirmModal(title, message, onConfirm) {
    const modal = document.getElementById('confirmModal');
    const titleEl = document.getElementById('confirmTitle');
    const messageEl = document.getElementById('confirmMessage');
    const actionBtn = document.getElementById('confirmActionBtn');

    if (titleEl) titleEl.textContent = title;
    if (messageEl) messageEl.textContent = message;
    if (actionBtn) actionBtn.onclick = onConfirm;

    Utils.showElement('confirmModal');
}

function closeConfirmModal() {
    Utils.hideElement('confirmModal');
}

function executeConfirmedAction() {
    // This function is called by the confirm button
    // The actual action is set in showConfirmModal
    closeConfirmModal();
}

/**
 * Troubleshooting functions
 */
function showTroubleshooting() {
    Utils.showElement('troubleshootingModal');
}

function closeTroubleshooting() {
    Utils.hideElement('troubleshootingModal');
}

/**
 * Error handling and recovery
 */
window.addEventListener('error', (event) => {
    console.error('Global error caught:', event.error);

    // Try to recover from common errors
    if (event.error.message.includes('camera')) {
        Utils.updateStatus('cameraStatus', 'error', 'Error');
        Utils.showError('Camera error detected. Please refresh the page and allow camera permissions.');
    } else if (event.error.message.includes('IndexedDB')) {
        Utils.updateStatus('storageStatus', 'error', 'Error');
        Utils.showError('Storage error detected. Please check if your browser supports IndexedDB.');
    }
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Unhandled promise rejection:', event.reason);

    // Prevent the default browser behavior
    event.preventDefault();

    // Show user-friendly error message
    Utils.showError('An unexpected error occurred. Please try refreshing the page.');
});

// Export functions for global access
window.proceedToFaceCapture = proceedToFaceCapture;
window.captureFace = captureFace;
window.retryCapture = retryCapture;
window.startAuthentication = startAuthentication;
window.retryAuthentication = retryAuthentication;
window.closeModal = closeModal;
window.handleModalAction = handleModalAction;
window.logout = logout;
window.editProfile = editProfile;
window.viewFullHistory = viewFullHistory;
window.updateFaceData = updateFaceData;
window.deleteAccount = deleteAccount;
window.testAuthentication = testAuthentication;
window.downloadData = downloadData;
window.clearData = clearData;
window.showConfirmModal = showConfirmModal;
window.closeConfirmModal = closeConfirmModal;
window.executeConfirmedAction = executeConfirmedAction;
window.showTroubleshooting = showTroubleshooting;
window.closeTroubleshooting = closeTroubleshooting;
