<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - Facial Authentication</title>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-content">
                <h1>Welcome to Your Dashboard</h1>
                <div class="user-menu">
                    <span class="user-greeting" id="userGreeting">Welcome back!</span>
                    <button class="btn btn-secondary btn-small" onclick="logout()">
                        Logout
                    </button>
                </div>
            </div>
        </header>

        <main class="main-content">
            <div class="dashboard-grid">
                <!-- User Profile Card -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>👤 Profile Information</h3>
                    </div>
                    <div class="card-content">
                        <div class="profile-info" id="profileInfo">
                            <div class="info-item">
                                <span class="info-label">Username:</span>
                                <span class="info-value" id="displayUsername">Loading...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Full Name:</span>
                                <span class="info-value" id="displayFullName">Loading...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Email:</span>
                                <span class="info-value" id="displayEmail">Loading...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Registration Date:</span>
                                <span class="info-value" id="displayRegDate">Loading...</span>
                            </div>
                        </div>
                        <button class="btn btn-secondary" onclick="editProfile()">
                            Edit Profile
                        </button>
                    </div>
                </div>

                <!-- Authentication History -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>🔐 Recent Authentication</h3>
                    </div>
                    <div class="card-content">
                        <div class="auth-history" id="authHistory">
                            <div class="history-item">
                                <span class="history-time" id="lastLoginTime">Loading...</span>
                                <span class="history-status success">✅ Successful Login</span>
                            </div>
                        </div>
                        <button class="btn btn-text" onclick="viewFullHistory()">
                            View Full History
                        </button>
                    </div>
                </div>

                <!-- Security Settings -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>🛡️ Security Settings</h3>
                    </div>
                    <div class="card-content">
                        <div class="security-options">
                            <div class="security-item">
                                <span class="security-label">Face Recognition</span>
                                <span class="security-status enabled">Enabled</span>
                            </div>
                            <div class="security-item">
                                <span class="security-label">Data Encryption</span>
                                <span class="security-status enabled">Enabled</span>
                            </div>
                            <div class="security-item">
                                <span class="security-label">Local Storage</span>
                                <span class="security-status enabled">Active</span>
                            </div>
                        </div>
                        <div class="security-actions">
                            <button class="btn btn-secondary" onclick="updateFaceData()">
                                Update Face Data
                            </button>
                            <button class="btn btn-danger" onclick="deleteAccount()">
                                Delete Account
                            </button>
                        </div>
                    </div>
                </div>

                <!-- System Information -->
                <div class="dashboard-card">
                    <div class="card-header">
                        <h3>ℹ️ System Information</h3>
                    </div>
                    <div class="card-content">
                        <div class="system-info">
                            <div class="info-item">
                                <span class="info-label">Browser:</span>
                                <span class="info-value" id="browserInfo">Loading...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Camera Support:</span>
                                <span class="info-value" id="cameraSupport">Loading...</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">Storage Used:</span>
                                <span class="info-value" id="storageUsed">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="quick-actions">
                <h3>Quick Actions</h3>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="testAuthentication()">
                        <span class="btn-icon">🧪</span>
                        Test Face Recognition
                    </button>
                    <button class="btn btn-secondary" onclick="downloadData()">
                        <span class="btn-icon">💾</span>
                        Export Data
                    </button>
                    <button class="btn btn-secondary" onclick="clearData()">
                        <span class="btn-icon">🗑️</span>
                        Clear All Data
                    </button>
                </div>
            </div>
        </main>

        <footer class="footer">
            <p>&copy; 2024 Facial Authentication System. Your data is secure and private.</p>
        </footer>
    </div>

    <!-- Confirmation Modal -->
    <div class="modal hidden" id="confirmModal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="confirmTitle">Confirm Action</h3>
                <button class="modal-close" onclick="closeConfirmModal()">&times;</button>
            </div>
            <div class="modal-body">
                <p id="confirmMessage">Are you sure you want to proceed?</p>
            </div>
            <div class="modal-footer">
                <button class="btn btn-secondary" onclick="closeConfirmModal()">
                    Cancel
                </button>
                <button class="btn btn-danger" id="confirmActionBtn" onclick="executeConfirmedAction()">
                    Confirm
                </button>
            </div>
        </div>
    </div>

    <script src="js/utils.js"></script>
    <script src="js/storage.js"></script>
    <script src="js/camera.js"></script>
    <script src="js/faceAuth.js"></script>
    <script src="js/app.js"></script>
</body>
</html>
