/**
 * Camera module for facial authentication system
 * Handles camera access, video streaming, and face detection visualization
 */

class CameraManager {
    constructor() {
        this.stream = null;
        this.video = null;
        this.canvas = null;
        this.context = null;
        this.isInitialized = false;
        this.constraints = {
            video: {
                width: { ideal: 640 },
                height: { ideal: 480 },
                facingMode: 'user'
            },
            audio: false
        };
    }

    /**
     * Initialize camera and video elements
     */
    async init(videoElementId = 'video', canvasElementId = 'overlay') {
        try {
            this.video = document.getElementById(videoElementId);
            this.canvas = document.getElementById(canvasElementId);
            
            if (!this.video) {
                throw new Error('Video element not found');
            }

            if (this.canvas) {
                this.context = this.canvas.getContext('2d');
            }

            await this.startCamera();
            this.isInitialized = true;
            return true;
        } catch (error) {
            console.error('Camera initialization failed:', error);
            throw error;
        }
    }

    /**
     * Start camera stream
     */
    async startCamera() {
        try {
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('Camera not supported in this browser');
            }

            this.stream = await navigator.mediaDevices.getUserMedia(this.constraints);
            this.video.srcObject = this.stream;
            
            return new Promise((resolve, reject) => {
                this.video.onloadedmetadata = () => {
                    this.video.play();
                    this.setupCanvas();
                    resolve();
                };
                
                this.video.onerror = () => {
                    reject(new Error('Failed to load video'));
                };
            });
        } catch (error) {
            console.error('Failed to start camera:', error);
            throw this.handleCameraError(error);
        }
    }

    /**
     * Setup canvas overlay for face detection visualization
     */
    setupCanvas() {
        if (!this.canvas || !this.video) return;

        const updateCanvasSize = () => {
            this.canvas.width = this.video.videoWidth;
            this.canvas.height = this.video.videoHeight;
            this.canvas.style.width = this.video.offsetWidth + 'px';
            this.canvas.style.height = this.video.offsetHeight + 'px';
        };

        // Update canvas size when video dimensions are available
        if (this.video.videoWidth > 0) {
            updateCanvasSize();
        } else {
            this.video.addEventListener('loadeddata', updateCanvasSize);
        }

        // Handle window resize
        window.addEventListener('resize', Utils.debounce(updateCanvasSize, 250));
    }

    /**
     * Stop camera stream
     */
    stopCamera() {
        if (this.stream) {
            this.stream.getTracks().forEach(track => {
                track.stop();
            });
            this.stream = null;
        }

        if (this.video) {
            this.video.srcObject = null;
        }

        this.clearCanvas();
        this.isInitialized = false;
    }

    /**
     * Capture current frame as image data
     */
    captureFrame() {
        if (!this.video || !this.isInitialized) {
            throw new Error('Camera not initialized');
        }

        const canvas = document.createElement('canvas');
        canvas.width = this.video.videoWidth;
        canvas.height = this.video.videoHeight;
        
        const context = canvas.getContext('2d');
        context.drawImage(this.video, 0, 0);
        
        return canvas;
    }

    /**
     * Get current frame as blob
     */
    async captureBlob(type = 'image/jpeg', quality = 0.8) {
        const canvas = this.captureFrame();
        
        return new Promise((resolve) => {
            canvas.toBlob(resolve, type, quality);
        });
    }

    /**
     * Draw face detection box on canvas overlay
     */
    drawFaceBox(detection, color = '#00ff00', lineWidth = 2) {
        if (!this.context || !detection) return;

        this.context.strokeStyle = color;
        this.context.lineWidth = lineWidth;
        this.context.fillStyle = 'transparent';

        const box = detection.detection.box;
        this.context.strokeRect(box.x, box.y, box.width, box.height);

        // Draw confidence score
        if (detection.detection.score) {
            const confidence = Math.round(detection.detection.score * 100);
            this.context.fillStyle = color;
            this.context.font = '16px Arial';
            this.context.fillText(
                `${confidence}%`, 
                box.x, 
                box.y - 5
            );
        }
    }

    /**
     * Draw face landmarks on canvas overlay
     */
    drawFaceLandmarks(landmarks, color = '#ff0000', radius = 2) {
        if (!this.context || !landmarks) return;

        this.context.fillStyle = color;
        
        landmarks.positions.forEach(point => {
            this.context.beginPath();
            this.context.arc(point.x, point.y, radius, 0, 2 * Math.PI);
            this.context.fill();
        });
    }

    /**
     * Clear canvas overlay
     */
    clearCanvas() {
        if (this.context && this.canvas) {
            this.context.clearRect(0, 0, this.canvas.width, this.canvas.height);
        }
    }

    /**
     * Draw authentication status overlay
     */
    drawAuthStatus(status, message, progress = 0) {
        if (!this.context) return;

        const canvas = this.canvas;
        const centerX = canvas.width / 2;
        const centerY = canvas.height / 2;

        // Semi-transparent background
        this.context.fillStyle = 'rgba(0, 0, 0, 0.7)';
        this.context.fillRect(0, 0, canvas.width, canvas.height);

        // Status circle
        const radius = 60;
        this.context.beginPath();
        this.context.arc(centerX, centerY, radius, 0, 2 * Math.PI);
        
        switch (status) {
            case 'detecting':
                this.context.strokeStyle = '#3182ce';
                break;
            case 'success':
                this.context.strokeStyle = '#38a169';
                break;
            case 'error':
                this.context.strokeStyle = '#e53e3e';
                break;
            default:
                this.context.strokeStyle = '#718096';
        }
        
        this.context.lineWidth = 4;
        this.context.stroke();

        // Progress arc
        if (progress > 0) {
            this.context.beginPath();
            this.context.arc(centerX, centerY, radius, -Math.PI / 2, 
                           -Math.PI / 2 + (2 * Math.PI * progress / 100));
            this.context.strokeStyle = '#667eea';
            this.context.lineWidth = 6;
            this.context.stroke();
        }

        // Status text
        this.context.fillStyle = 'white';
        this.context.font = 'bold 16px Arial';
        this.context.textAlign = 'center';
        this.context.fillText(message, centerX, centerY + radius + 30);
    }

    /**
     * Handle camera errors with user-friendly messages
     */
    handleCameraError(error) {
        let userMessage = 'Camera access failed';
        
        if (error.name === 'NotAllowedError') {
            userMessage = 'Camera permission denied. Please allow camera access and try again.';
        } else if (error.name === 'NotFoundError') {
            userMessage = 'No camera found. Please connect a camera and try again.';
        } else if (error.name === 'NotReadableError') {
            userMessage = 'Camera is being used by another application. Please close other apps and try again.';
        } else if (error.name === 'OverconstrainedError') {
            userMessage = 'Camera does not support the required settings.';
        } else if (error.name === 'SecurityError') {
            userMessage = 'Camera access blocked due to security restrictions.';
        }

        return new Error(userMessage);
    }

    /**
     * Check camera permissions
     */
    async checkPermissions() {
        try {
            if (!navigator.permissions) {
                return 'unknown';
            }

            const permission = await navigator.permissions.query({ name: 'camera' });
            return permission.state; // 'granted', 'denied', or 'prompt'
        } catch (error) {
            console.warn('Could not check camera permissions:', error);
            return 'unknown';
        }
    }

    /**
     * Get available camera devices
     */
    async getAvailableDevices() {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            return devices.filter(device => device.kind === 'videoinput');
        } catch (error) {
            console.error('Failed to enumerate devices:', error);
            return [];
        }
    }

    /**
     * Switch to a different camera device
     */
    async switchCamera(deviceId) {
        try {
            this.stopCamera();
            
            this.constraints.video.deviceId = { exact: deviceId };
            await this.startCamera();
            
            return true;
        } catch (error) {
            console.error('Failed to switch camera:', error);
            throw error;
        }
    }

    /**
     * Take a photo and return as data URL
     */
    takePhoto() {
        const canvas = this.captureFrame();
        return canvas.toDataURL('image/jpeg', 0.8);
    }

    /**
     * Get video stream statistics
     */
    getStreamStats() {
        if (!this.video || !this.stream) {
            return null;
        }

        const videoTrack = this.stream.getVideoTracks()[0];
        const settings = videoTrack.getSettings();
        
        return {
            width: settings.width,
            height: settings.height,
            frameRate: settings.frameRate,
            facingMode: settings.facingMode,
            deviceId: settings.deviceId
        };
    }

    /**
     * Check if camera is active
     */
    isActive() {
        return this.isInitialized && this.stream && this.stream.active;
    }
}

// Create global camera instance
const camera = new CameraManager();

// Export for use in other modules
window.Camera = camera;
