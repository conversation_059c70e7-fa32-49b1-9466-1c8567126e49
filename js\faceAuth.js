/**
 * Facial authentication module using face-api.js
 * Handles face detection, recognition, and authentication logic
 */

class FacialAuthentication {
    constructor() {
        this.isLoaded = false;
        this.detectionOptions = null;
        this.recognitionThreshold = 0.6; // Similarity threshold for recognition
        this.detectionInterval = null;
        this.currentDetections = [];
        this.isDetecting = false;
    }

    /**
     * Initialize face-api.js models
     */
    async init() {
        try {
            Utils.updateStatus('faceApiStatus', 'loading', 'Loading models...');

            // Try loading from CDN first (more reliable)
            await this.loadModelsFromCDN();

            // Set detection options
            this.detectionOptions = new faceapi.TinyFaceDetectorOptions({
                inputSize: 416,
                scoreThreshold: 0.3  // Lower threshold for better detection
            });

            this.isLoaded = true;
            Utils.updateStatus('faceApiStatus', 'ready', 'Ready');
            return true;
        } catch (error) {
            console.error('Failed to load face-api models:', error);
            Utils.updateStatus('faceApiStatus', 'error', 'Failed to load');
            throw new Error('Failed to load face recognition models. Please check your internet connection.');
        }
    }

    /**
     * Load models from CDN
     */
    async loadModelsFromCDN() {
        // Use the official face-api.js CDN
        const modelBaseUrl = 'https://cdn.jsdelivr.net/npm/face-api.js@0.22.2/weights';

        console.log('Loading face-api.js models from CDN...');

        try {
            await Promise.all([
                faceapi.nets.tinyFaceDetector.loadFromUri(modelBaseUrl),
                faceapi.nets.faceLandmark68Net.loadFromUri(modelBaseUrl),
                faceapi.nets.faceRecognitionNet.loadFromUri(modelBaseUrl)
            ]);
            console.log('Face-api.js models loaded successfully from CDN');
        } catch (error) {
            console.error('Failed to load from primary CDN, trying alternative...', error);

            // Try alternative CDN
            const altModelBaseUrl = 'https://raw.githubusercontent.com/justadudewhohacks/face-api.js/master/weights';
            await Promise.all([
                faceapi.nets.tinyFaceDetector.loadFromUri(altModelBaseUrl),
                faceapi.nets.faceLandmark68Net.loadFromUri(altModelBaseUrl),
                faceapi.nets.faceRecognitionNet.loadFromUri(altModelBaseUrl)
            ]);
            console.log('Face-api.js models loaded from alternative CDN');
        }
    }

    /**
     * Detect faces in video stream
     */
    async detectFaces(videoElement) {
        if (!this.isLoaded) {
            throw new Error('Face API not initialized');
        }

        try {
            const detections = await faceapi
                .detectAllFaces(videoElement, this.detectionOptions)
                .withFaceLandmarks()
                .withFaceDescriptors();

            return detections;
        } catch (error) {
            console.error('Face detection failed:', error);
            return [];
        }
    }

    /**
     * Start continuous face detection
     */
    startDetection(videoElement, onDetection, interval = 100) {
        if (this.isDetecting) {
            this.stopDetection();
        }

        this.isDetecting = true;
        
        const detect = async () => {
            if (!this.isDetecting) return;

            try {
                const detections = await this.detectFaces(videoElement);
                this.currentDetections = detections;
                
                if (onDetection) {
                    onDetection(detections);
                }
            } catch (error) {
                console.error('Detection error:', error);
            }

            if (this.isDetecting) {
                setTimeout(detect, interval);
            }
        };

        detect();
    }

    /**
     * Stop continuous face detection
     */
    stopDetection() {
        this.isDetecting = false;
        this.currentDetections = [];
        Camera.clearCanvas();
    }

    /**
     * Extract face descriptor for registration
     */
    async extractFaceDescriptor(videoElement) {
        if (!this.isLoaded) {
            throw new Error('Face API not initialized');
        }

        const detections = await this.detectFaces(videoElement);
        
        if (detections.length === 0) {
            throw new Error('No face detected. Please ensure your face is visible and well-lit.');
        }

        if (detections.length > 1) {
            throw new Error('Multiple faces detected. Please ensure only one person is in the frame.');
        }

        const detection = detections[0];
        
        // Check detection quality (more lenient threshold)
        if (detection.detection.score < 0.5) {
            throw new Error('Face detection confidence too low. Please improve lighting and face positioning.');
        }

        return {
            descriptor: Array.from(detection.descriptor),
            landmarks: detection.landmarks,
            detection: detection.detection,
            confidence: detection.detection.score
        };
    }

    /**
     * Compare face descriptors for authentication
     */
    compareFaces(descriptor1, descriptor2) {
        if (!descriptor1 || !descriptor2) {
            return { distance: 1, similarity: 0, match: false };
        }

        try {
            const distance = faceapi.euclideanDistance(descriptor1, descriptor2);
            const similarity = Math.max(0, 1 - distance);
            const match = distance < this.recognitionThreshold;

            return {
                distance,
                similarity,
                match,
                confidence: similarity
            };
        } catch (error) {
            console.error('Face comparison failed:', error);
            return { distance: 1, similarity: 0, match: false };
        }
    }

    /**
     * Authenticate user against stored face data
     */
    async authenticateUser(videoElement, storedUsers) {
        if (!this.isLoaded) {
            throw new Error('Face API not initialized');
        }

        if (!storedUsers || storedUsers.length === 0) {
            throw new Error('No registered users found');
        }

        try {
            const currentFace = await this.extractFaceDescriptor(videoElement);
            let bestMatch = null;
            let bestSimilarity = 0;

            // Compare against all stored users
            for (const user of storedUsers) {
                if (!user.faceDescriptor) continue;

                const comparison = this.compareFaces(
                    currentFace.descriptor,
                    user.faceDescriptor
                );

                if (comparison.match && comparison.similarity > bestSimilarity) {
                    bestMatch = user;
                    bestSimilarity = comparison.similarity;
                }
            }

            if (bestMatch) {
                return {
                    success: true,
                    user: bestMatch,
                    confidence: bestSimilarity,
                    message: `Welcome back, ${bestMatch.fullName}!`
                };
            } else {
                return {
                    success: false,
                    user: null,
                    confidence: bestSimilarity,
                    message: 'Face not recognized. Please try again or register.'
                };
            }
        } catch (error) {
            return {
                success: false,
                user: null,
                confidence: 0,
                message: error.message
            };
        }
    }

    /**
     * Validate face quality for registration
     */
    async validateFaceQuality(videoElement) {
        try {
            const detections = await this.detectFaces(videoElement);
            
            if (detections.length === 0) {
                return {
                    valid: false,
                    message: 'No face detected',
                    suggestions: ['Ensure your face is visible', 'Improve lighting', 'Move closer to camera']
                };
            }

            if (detections.length > 1) {
                return {
                    valid: false,
                    message: 'Multiple faces detected',
                    suggestions: ['Ensure only one person is in frame']
                };
            }

            const detection = detections[0];
            const confidence = detection.detection.score;
            const box = detection.detection.box;

            // Check various quality metrics
            const issues = [];
            const suggestions = [];

            if (confidence < 0.6) {
                issues.push('Low detection confidence');
                suggestions.push('Improve lighting conditions');
            }

            if (box.width < 100 || box.height < 100) {
                issues.push('Face too small');
                suggestions.push('Move closer to the camera');
            }

            if (box.width > 400 || box.height > 400) {
                issues.push('Face too large');
                suggestions.push('Move further from the camera');
            }

            // Check if face is centered
            const videoWidth = videoElement.videoWidth;
            const videoHeight = videoElement.videoHeight;
            const centerX = videoWidth / 2;
            const centerY = videoHeight / 2;
            const faceX = box.x + box.width / 2;
            const faceY = box.y + box.height / 2;

            if (Math.abs(faceX - centerX) > videoWidth * 0.2) {
                issues.push('Face not centered horizontally');
                suggestions.push('Center your face in the frame');
            }

            if (Math.abs(faceY - centerY) > videoHeight * 0.2) {
                issues.push('Face not centered vertically');
                suggestions.push('Center your face in the frame');
            }

            return {
                valid: issues.length === 0,
                confidence: confidence,
                message: issues.length === 0 ? 'Face quality good' : issues.join(', '),
                suggestions: suggestions,
                detection: detection
            };
        } catch (error) {
            return {
                valid: false,
                message: 'Face validation failed',
                suggestions: ['Check camera permissions', 'Ensure good lighting']
            };
        }
    }

    /**
     * Draw detection results on canvas
     */
    drawDetections(detections, canvas) {
        if (!detections || detections.length === 0) {
            Camera.clearCanvas();
            return;
        }

        Camera.clearCanvas();

        detections.forEach(detection => {
            // Draw face box
            Camera.drawFaceBox(detection, '#00ff00', 2);

            // Draw landmarks if available
            if (detection.landmarks) {
                Camera.drawFaceLandmarks(detection.landmarks, '#ff0000', 1);
            }
        });
    }

    /**
     * Get current detection status
     */
    getDetectionStatus() {
        if (!this.isDetecting) {
            return { status: 'idle', count: 0, message: 'Detection stopped' };
        }

        const count = this.currentDetections.length;
        
        if (count === 0) {
            return { status: 'no_face', count: 0, message: 'No face detected' };
        } else if (count === 1) {
            const confidence = this.currentDetections[0].detection.score;
            return { 
                status: 'face_detected', 
                count: 1, 
                confidence: confidence,
                message: `Face detected (${Math.round(confidence * 100)}%)` 
            };
        } else {
            return { status: 'multiple_faces', count: count, message: `${count} faces detected` };
        }
    }

    /**
     * Set recognition threshold
     */
    setRecognitionThreshold(threshold) {
        this.recognitionThreshold = Math.max(0, Math.min(1, threshold));
    }

    /**
     * Get recognition threshold
     */
    getRecognitionThreshold() {
        return this.recognitionThreshold;
    }

    /**
     * Check if face API is ready
     */
    isReady() {
        return this.isLoaded;
    }
}

// Create global facial authentication instance
const faceAuth = new FacialAuthentication();

// Export for use in other modules
window.FaceAuth = faceAuth;
